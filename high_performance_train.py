#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能5G日志异常检测 - 目标F1-macro > 0.50
使用多种先进技术：深度特征工程、高级集成学习、智能重采样
"""

import os
import json
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, ExtraTreesClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, f1_score, confusion_matrix
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from sklearn.decomposition import TruncatedSVD
import xgboost as xgb
import lightgbm as lgb
from collections import Counter
import re
from imblearn.over_sampling import SMOTE, ADASYN, BorderlineSMOTE
from imblearn.combine import SMOTEENN, SMOTETomek
import warnings
warnings.filterwarnings('ignore')

class AdvancedLogAnalyzer:
    def __init__(self):
        self.vectorizers = {}
        self.scalers = {}
        self.feature_selectors = {}
        self.models = {}
        
    def load_data(self):
        """加载数据"""
        print("🔄 加载数据...")
        
        # 加载标签
        label_file = 'files/train/label.csv'
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        labels_df = None
        
        for encoding in encodings:
            try:
                labels_df = pd.read_csv(label_file, encoding=encoding)
                print(f"✅ 成功使用 {encoding} 编码加载标签文件: {len(labels_df)} 条记录")
                break
            except UnicodeDecodeError:
                continue
        
        if labels_df is None:
            labels_df = pd.read_csv(label_file, encoding='utf-8', errors='ignore')
        
        # 加载日志文件
        logs = []
        labels = []
        
        for _, row in labels_df.iterrows():
            log_id = int(row['日志片段文件编号'])
            label = int(row['故障类型'])
            log_file = f'files/train/{log_id}.txt'
            
            if os.path.exists(log_file):
                content = self._read_log_file(log_file)
                logs.append(content)
                labels.append(label)
        
        print(f"✅ 成功加载 {len(logs)} 个样本")
        return logs, labels
    
    def _read_log_file(self, file_path):
        """读取日志文件"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()
                
                # 解析JSON格式
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        if 'content' in json_data:
                            return json_data['content']
                    except:
                        pass
                return content
            except UnicodeDecodeError:
                continue
        return ""
    
    def extract_deep_features(self, logs):
        """深度特征工程"""
        print("🔧 提取深度特征...")
        
        features = []
        for log in logs:
            if not log:
                features.append([0] * 50)
                continue
                
            feat = []
            lines = [line.strip() for line in log.split('\n') if line.strip()]
            text_lower = log.lower()
            
            # === 基础统计特征 ===
            feat.append(len(log))  # 总字符数
            feat.append(len(lines))  # 行数
            feat.append(len(log.split()))  # 词数
            feat.append(np.mean([len(line) for line in lines]) if lines else 0)  # 平均行长
            feat.append(np.std([len(line) for line in lines]) if len(lines) > 1 else 0)  # 行长标准差
            
            # === 日志级别特征 ===
            total_lines = len(lines)
            info_count = log.count('[INFO]')
            error_count = log.count('[ERROR]')
            warn_count = log.count('[WARN]')
            debug_count = log.count('[DEBUG]')
            trace_count = log.count('[TRACE]')
            
            feat.extend([info_count, error_count, warn_count, debug_count, trace_count])
            
            # 日志级别比例
            if total_lines > 0:
                feat.extend([
                    info_count / total_lines,
                    error_count / total_lines,
                    warn_count / total_lines,
                    debug_count / total_lines
                ])
            else:
                feat.extend([0, 0, 0, 0])
            
            # === 5G核心网模块特征 ===
            modules = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF', 'NRF', 'NEF']
            module_counts = [log.count(f'[{module}]') for module in modules]
            feat.extend(module_counts)
            
            # 模块多样性
            active_modules = sum(1 for count in module_counts if count > 0)
            feat.append(active_modules)
            
            # === HTTP状态码特征 ===
            http_codes = ['200', '201', '204', '400', '401', '403', '404', '500', '502', '503']
            for code in http_codes:
                feat.append(log.count(f'| {code} |'))
            
            # === 错误和异常特征 ===
            error_keywords = ['failed', 'error', 'timeout', 'exception', 'invalid', 'denied', 'reject']
            for keyword in error_keywords:
                feat.append(text_lower.count(keyword))
            
            # === 网络和标识符特征 ===
            feat.append(len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', log)))  # IP地址
            feat.append(len(re.findall(r'imsi-\d+', log)))  # IMSI
            feat.append(len(re.findall(r'urn:uuid:', log)))  # UUID
            feat.append(len(re.findall(r':\d{2,5}\b', log)))  # 端口号
            
            # === 时间特征 ===
            timestamps = re.findall(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', log)
            feat.append(len(timestamps))
            
            features.append(feat)
        
        return np.array(features)
    
    def extract_advanced_text_features(self, logs):
        """高级文本特征提取"""
        print("📝 提取高级文本特征...")
        
        # 预处理文本
        processed_logs = []
        for log in logs:
            text = log[:15000] if log else ""  # 增加文本长度
            
            # 保留更多语义信息的预处理
            text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIMESTAMP>', text)
            text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
            text = re.sub(r'imsi-\d+', '<IMSI>', text)
            text = re.sub(r'urn:uuid:[a-f0-9-]+', '<UUID>', text)
            text = re.sub(r':\d{2,5}\b', ':<PORT>', text)
            text = re.sub(r'\|\s*\d+\s*\|', '|<STATUS>|', text)
            
            processed_logs.append(text)
        
        # 多种文本特征
        features_list = []
        
        # 1. TF-IDF特征 (1-3 gram)
        tfidf = TfidfVectorizer(
            max_features=1500,
            ngram_range=(1, 3),
            min_df=2,
            max_df=0.7,
            sublinear_tf=True,
            analyzer='word'
        )
        tfidf_features = tfidf.fit_transform(processed_logs).toarray()
        features_list.append(tfidf_features)
        self.vectorizers['tfidf'] = tfidf
        
        # 2. 字符级TF-IDF
        char_tfidf = TfidfVectorizer(
            max_features=500,
            ngram_range=(3, 5),
            analyzer='char',
            min_df=2,
            max_df=0.8
        )
        char_features = char_tfidf.fit_transform(processed_logs).toarray()
        features_list.append(char_features)
        self.vectorizers['char_tfidf'] = char_tfidf
        
        # 3. Count特征
        count_vec = CountVectorizer(
            max_features=800,
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.8
        )
        count_features = count_vec.fit_transform(processed_logs).toarray()
        features_list.append(count_features)
        self.vectorizers['count'] = count_vec
        
        # 合并所有文本特征
        text_features = np.hstack(features_list)
        
        # 降维
        svd = TruncatedSVD(n_components=min(1000, text_features.shape[1]), random_state=42)
        text_features_reduced = svd.fit_transform(text_features)
        self.feature_selectors['svd'] = svd
        
        return text_features_reduced
    
    def create_advanced_models(self):
        """创建高级模型集合"""
        models = {
            'xgb_1': xgb.XGBClassifier(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1,
                random_state=42,
                eval_metric='mlogloss'
            ),
            'xgb_2': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=10,
                learning_rate=0.08,
                subsample=0.9,
                colsample_bytree=0.7,
                reg_alpha=0.05,
                reg_lambda=0.5,
                random_state=123,
                eval_metric='mlogloss'
            ),
            'lgb': lgb.LGBMClassifier(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=1,
                random_state=42,
                verbose=-1
            ),
            'rf': RandomForestClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            'et': ExtraTreesClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=3,
                min_samples_leaf=1,
                max_features='sqrt',
                random_state=42,
                class_weight='balanced',
                n_jobs=-1
            ),
            'gb': GradientBoostingClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.05,
                subsample=0.8,
                random_state=42
            )
        }
        return models
    
    def smart_resampling(self, X, y):
        """智能重采样策略"""
        print("⚖️ 应用智能重采样...")
        
        unique_classes, counts = np.unique(y, return_counts=True)
        min_samples = min(counts)
        
        if min_samples < 3:
            print("样本过少，跳过重采样")
            return X, y
        
        # 尝试多种重采样策略
        strategies = [
            ('SMOTE', SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))),
            ('BorderlineSMOTE', BorderlineSMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))),
            ('ADASYN', ADASYN(random_state=42, n_neighbors=min(3, min_samples - 1))),
            ('SMOTEENN', SMOTEENN(random_state=42, smote=SMOTE(k_neighbors=min(3, min_samples - 1))))
        ]
        
        best_strategy = None
        best_score = 0
        
        for name, sampler in strategies:
            try:
                X_resampled, y_resampled = sampler.fit_resample(X, y)
                
                # 快速评估重采样效果
                rf_quick = RandomForestClassifier(n_estimators=50, random_state=42)
                scores = cross_val_score(rf_quick, X_resampled, y_resampled, 
                                       cv=3, scoring='f1_macro')
                score = scores.mean()
                
                print(f"{name}: F1-macro = {score:.4f}")
                
                if score > best_score:
                    best_score = score
                    best_strategy = (name, X_resampled, y_resampled)
                    
            except Exception as e:
                print(f"{name} 失败: {e}")
                continue
        
        if best_strategy:
            print(f"选择最佳重采样策略: {best_strategy[0]}")
            return best_strategy[1], best_strategy[2]
        else:
            return X, y

    def train_stacked_ensemble(self, X, y):
        """训练堆叠集成模型"""
        print("🚀 训练高级堆叠集成模型...")

        # 数据分割
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # 特征缩放
        scaler = RobustScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        self.scalers['main'] = scaler

        # 智能重采样
        X_train_balanced, y_train_balanced = self.smart_resampling(X_train_scaled, y_train)

        # 创建模型
        models = self.create_advanced_models()

        # 第一层：基础模型
        base_predictions = []
        trained_models = {}
        cv_scores = {}

        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        for name, model in models.items():
            print(f"训练 {name}...")

            # 交叉验证评估
            try:
                scores = cross_val_score(model, X_train_balanced, y_train_balanced,
                                       cv=cv, scoring='f1_macro', n_jobs=-1)
                cv_scores[name] = scores.mean()
                print(f"{name} CV F1-macro: {scores.mean():.4f} ± {scores.std():.4f}")

                # 训练完整模型
                model.fit(X_train_balanced, y_train_balanced)
                trained_models[name] = model

                # 获取预测概率作为元特征
                pred_proba = model.predict_proba(X_test_scaled)
                base_predictions.append(pred_proba)

            except Exception as e:
                print(f"训练 {name} 失败: {e}")
                continue

        # 选择最佳模型
        if cv_scores:
            best_model_name = max(cv_scores, key=cv_scores.get)
            best_single_model = trained_models[best_model_name]
            best_single_score = cv_scores[best_model_name]

            print(f"\n🏆 最佳单模型: {best_model_name} (CV F1-macro: {best_single_score:.4f})")

            # 第二层：元学习器
            if len(base_predictions) >= 2:
                print("构建元学习器...")

                # 合并基础模型预测作为元特征
                meta_features = np.hstack(base_predictions)

                # 元学习器
                meta_learner = LogisticRegression(
                    random_state=42,
                    class_weight='balanced',
                    max_iter=1000,
                    C=0.1
                )

                meta_learner.fit(meta_features, y_test)

                # 创建最终集成模型
                final_ensemble = VotingClassifier(
                    estimators=[(name, model) for name, model in trained_models.items()
                              if cv_scores[name] > best_single_score - 0.03],
                    voting='soft'
                )

                final_ensemble.fit(X_train_balanced, y_train_balanced)

                # 评估集成模型
                ensemble_pred = final_ensemble.predict(X_test_scaled)
                ensemble_f1 = f1_score(y_test, ensemble_pred, average='macro')

                print(f"集成模型 F1-macro: {ensemble_f1:.4f}")

                # 选择最佳模型
                if ensemble_f1 > best_single_score:
                    final_model = final_ensemble
                    final_score = ensemble_f1
                    print("✅ 使用集成模型")
                else:
                    final_model = best_single_model
                    final_score = best_single_score
                    print("✅ 使用最佳单模型")
            else:
                final_model = best_single_model
                final_score = best_single_score

            # 最终评估
            final_pred = final_model.predict(X_test_scaled)
            final_f1 = f1_score(y_test, final_pred, average='macro')

            print(f"\n🎯 最终验证集 F1-macro: {final_f1:.4f}")

            if final_f1 >= 0.50:
                print("🎉 成功达到目标分数 0.50+!")
            else:
                print("⚠️ 未达到目标分数，需要进一步优化")

            print("\n详细分类报告:")
            print(classification_report(y_test, final_pred))

            return final_model, final_f1

        else:
            print("❌ 所有模型训练失败")
            return None, 0.0

    def predict_test_set(self, model):
        """预测测试集"""
        print("🔮 预测测试集...")

        test_dir = 'files/test'
        if not os.path.exists(test_dir):
            print("测试目录不存在")
            return None

        test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
        test_files.sort(key=lambda x: int(x.split('.')[0]))

        test_logs = []
        test_ids = []

        for file_name in test_files:
            log_id = int(file_name.split('.')[0])
            file_path = os.path.join(test_dir, file_name)
            content = self._read_log_file(file_path)
            test_logs.append(content)
            test_ids.append(log_id)

        # 提取特征
        deep_features = self.extract_deep_features(test_logs)
        text_features = self.extract_advanced_text_features(test_logs)

        # 合并特征
        X_test = np.hstack([deep_features, text_features])
        X_test_scaled = self.scalers['main'].transform(X_test)

        # 预测
        predictions = model.predict(X_test_scaled)

        # 保存结果
        results_df = pd.DataFrame({
            '日志片段文件编号': test_ids,
            '故障类型': predictions.astype(int)
        })

        results_df = results_df.sort_values('日志片段文件编号')
        results_df.to_csv('result_high_performance.csv', index=False, encoding='utf-8-sig')

        print(f"✅ 预测完成，保存到 result_high_performance.csv")
        print("预测分布:")
        pred_counts = Counter(predictions)
        for fault_type, count in sorted(pred_counts.items()):
            print(f"  故障类型 {fault_type}: {count} 个")

        return results_df

def main():
    """主函数"""
    print("🚀 高性能5G日志异常检测系统启动")
    print("目标: 验证集F1-macro > 0.50")
    print("=" * 60)

    analyzer = AdvancedLogAnalyzer()

    # 1. 加载数据
    logs, labels = analyzer.load_data()

    # 2. 数据分布分析
    print("\n📊 数据分布:")
    label_counts = Counter(labels)
    for label, count in sorted(label_counts.items()):
        print(f"  故障类型 {label}: {count} 个样本 ({count/len(labels)*100:.1f}%)")

    # 3. 特征工程
    deep_features = analyzer.extract_deep_features(logs)
    text_features = analyzer.extract_advanced_text_features(logs)

    # 4. 合并特征
    X = np.hstack([deep_features, text_features])
    y = np.array(labels)

    print(f"\n🔧 特征矩阵形状: {X.shape}")
    print(f"深度特征: {deep_features.shape[1]} 维")
    print(f"文本特征: {text_features.shape[1]} 维")

    # 5. 训练高级模型
    model, f1_score = analyzer.train_stacked_ensemble(X, y)

    if model is not None and f1_score >= 0.50:
        # 6. 预测测试集
        results = analyzer.predict_test_set(model)
        print(f"\n🎉 任务完成！最终F1-macro: {f1_score:.4f}")
        return analyzer, model, f1_score
    else:
        print(f"\n❌ 未达到目标分数。当前F1-macro: {f1_score:.4f}")
        print("建议：增加更多特征工程或调整模型参数")
        return analyzer, model, f1_score

if __name__ == "__main__":
    main()
