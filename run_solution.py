#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运行5G日志异常检测解决方案
"""

import sys
import os

def check_dependencies():
    """检查依赖库"""
    required_packages = [
        'pandas', 'numpy', 'scikit-learn',
        'xgboost', 'lightgbm'
    ]

    missing_packages = []

    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    if missing_packages:
        print("缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"  - {pkg}")
        print("\n请安装缺少的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False

    return True

def run_basic_solution():
    """运行基础解决方案"""
    print("运行基础解决方案...")

    try:
        from log_anomaly_detection import LogAnomalyDetector

        detector = LogAnomalyDetector()
        results = detector.run_complete_pipeline()

        if results:
            print("\n基础解决方案运行成功！")
            return True
        else:
            print("基础解决方案运行失败！")
            return False

    except Exception as e:
        print(f"基础解决方案运行出错: {e}")
        return False

def run_optimized_solution():
    """运行优化解决方案"""
    print("运行优化解决方案...")

    try:
        from optimized_solution import OptimizedLogDetector

        detector = OptimizedLogDetector()
        results = detector.run_training()

        if results:
            print("\n优化解决方案运行成功！")
            print(f"最佳F1-macro分数: {results['best_score']:.4f}")
            return True
        else:
            print("优化解决方案运行失败！")
            return False

    except Exception as e:
        print(f"优化解决方案运行出错: {e}")
        return False

def main():
    """主函数"""
    print("=== 5G核心网日志异常检测系统 ===")
    print("检查环境和依赖...")

    # 检查数据目录
    if not os.path.exists('files/train'):
        print("错误: 找不到数据目录 'files/train'")
        print("请确保数据文件在正确的位置")
        return

    # 检查标签文件
    if not os.path.exists('files/train/label.csv'):
        print("错误: 找不到标签文件 'files/train/label.csv'")
        return

    # 检查依赖
    if not check_dependencies():
        return

    print("环境检查通过！\n")

    # 运行解决方案
    print("选择运行方案:")
    print("1. 基础解决方案 (完整特征工程)")
    print("2. 优化解决方案 (针对F1-score优化)")
    print("3. 两个都运行")

    choice = input("请选择 (1/2/3): ").strip()

    if choice == '1':
        run_basic_solution()
    elif choice == '2':
        run_optimized_solution()
    elif choice == '3':
        print("运行基础解决方案...")
        run_basic_solution()
        print("\n" + "="*50 + "\n")
        print("运行优化解决方案...")
        run_optimized_solution()
    else:
        print("无效选择，运行优化解决方案...")
        run_optimized_solution()

if __name__ == "__main__":
    main()