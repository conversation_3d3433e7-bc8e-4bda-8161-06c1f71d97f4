#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的5G核心网日志异常检测解决方案
专门针对Macro F1-score优化，处理类别不平衡问题
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, GridSearchCV
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.metrics import f1_score, make_scorer
from sklearn.utils.class_weight import compute_class_weight
try:
    from imblearn.over_sampling import SMOTE
    from imblearn.under_sampling import RandomUnderSampler
    from imblearn.pipeline import Pipeline as ImbPipeline
    IMBALANCED_LEARN_AVAILABLE = True
except ImportError:
    IMBALANCED_LEARN_AVAILABLE = False
    print("imbalanced-learn未安装，将使用类别权重处理不平衡")

import xgboost as xgb
import lightgbm as lgb

class OptimizedLogDetector:
    """优化的日志异常检测器"""

    def __init__(self, data_dir='files/train'):
        self.data_dir = data_dir
        self.label_file = os.path.join(data_dir, 'label.csv')
        self.logs_data = []
        self.best_model = None
        self.feature_columns = None
        self.scaler = None
        self.vectorizer = None

    def load_and_preprocess_data(self):
        """加载和预处理数据"""
        print("加载数据...")

        # 加载标签
        try:
            labels_df = pd.read_csv(self.label_file, encoding='utf-8-sig')
            labels_df.columns = ['file_id', 'fault_type']
        except:
            try:
                labels_df = pd.read_csv(self.label_file, encoding='gbk')
                labels_df.columns = ['file_id', 'fault_type']
            except:
                labels_df = pd.read_csv(self.label_file, encoding='utf-8', errors='ignore')
                labels_df.columns = ['file_id', 'fault_type']

        print(f"标签数据: {len(labels_df)} 条")

        # 加载日志文件
        log_files = [f for f in os.listdir(self.data_dir) if f.endswith('.txt')]
        log_files.sort(key=lambda x: int(x.split('.')[0]))

        for log_file in log_files:
            file_id = int(log_file.split('.')[0])
            file_path = os.path.join(self.data_dir, log_file)

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()

                # 解析JSON格式
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        log_content = json_data.get('content', '')
                    except:
                        log_content = content
                else:
                    log_content = content

                # 获取标签
                label_row = labels_df[labels_df['file_id'] == file_id]
                if not label_row.empty:
                    fault_type = label_row.iloc[0]['fault_type']
                    self.logs_data.append({
                        'file_id': file_id,
                        'content': log_content,
                        'fault_type': fault_type
                    })
            except Exception as e:
                print(f"跳过文件 {log_file}: {e}")
                continue

        print(f"成功加载 {len(self.logs_data)} 个样本")
        return len(self.logs_data) > 0

    def extract_advanced_features(self):
        """提取高级特征"""
        print("提取特征...")

        features_list = []
        texts = []

        for item in self.logs_data:
            content = item['content']
            texts.append(content)

            features = {}

            # 基础统计特征
            features['log_length'] = len(content)
            features['line_count'] = content.count('\n') + 1
            features['word_count'] = len(content.split())
            features['char_diversity'] = len(set(content)) / max(len(content), 1)

            # 时间特征
            time_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})'
            time_matches = re.findall(time_pattern, content)
            features['time_entries'] = len(time_matches)

            if time_matches:
                try:
                    times = [datetime.fromisoformat(t) for t in time_matches]
                    if len(times) > 1:
                        time_span = (max(times) - min(times)).total_seconds()
                        features['time_span'] = time_span
                        features['log_rate'] = len(times) / max(time_span, 1)
                    else:
                        features['time_span'] = 0
                        features['log_rate'] = 0
                except:
                    features['time_span'] = 0
                    features['log_rate'] = 0
            else:
                features['time_span'] = 0
                features['log_rate'] = 0

            # 日志级别特征
            features['error_count'] = len(re.findall(r'\[ERROR\]', content))
            features['warn_count'] = len(re.findall(r'\[WARN\]', content))
            features['info_count'] = len(re.findall(r'\[INFO\]', content))
            features['debug_count'] = len(re.findall(r'\[DEBU\]', content))

            # 网络模块特征
            modules = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF']
            for module in modules:
                features[f'{module.lower()}_count'] = len(re.findall(rf'\[{module}\]', content))

            # HTTP状态码特征
            http_codes = re.findall(r'\|\s*(\d{3})\s*\|', content)
            features['http_2xx'] = sum(1 for code in http_codes if code.startswith('2'))
            features['http_4xx'] = sum(1 for code in http_codes if code.startswith('4'))
            features['http_5xx'] = sum(1 for code in http_codes if code.startswith('5'))

            # 错误关键词特征
            error_keywords = ['failed', 'error', 'timeout', 'invalid', 'denied', 'exception']
            features['error_keywords'] = sum(content.lower().count(kw) for kw in error_keywords)

            # IP地址和标识符特征
            features['unique_ips'] = len(set(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content)))
            features['unique_imsi'] = len(set(re.findall(r'imsi-(\d+)', content)))
            features['unique_sessions'] = len(set(re.findall(r'urn:uuid:[\w-]+', content)))

            features['fault_type'] = item['fault_type']
            features_list.append(features)

        # 创建特征DataFrame
        features_df = pd.DataFrame(features_list)

        # 提取TF-IDF特征
        self.vectorizer = TfidfVectorizer(
            max_features=500,
            ngram_range=(1, 2),
            min_df=2,
            max_df=0.95,
            stop_words=None
        )

        tfidf_features = self.vectorizer.fit_transform(texts)
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        )

        # 合并特征
        features_df = pd.concat([features_df.reset_index(drop=True),
                                tfidf_df.reset_index(drop=True)], axis=1)

        print(f"特征提取完成: {features_df.shape[1]-1} 个特征")
        return features_df

    def train_optimized_models(self, features_df):
        """训练优化的模型"""
        print("训练模型...")

        # 准备数据
        X = features_df.drop('fault_type', axis=1)
        y = features_df['fault_type']

        # 填充缺失值
        X = X.fillna(0)

        # 特征标准化
        self.scaler = RobustScaler()
        X_scaled = self.scaler.fit_transform(X)
        self.feature_columns = X.columns.tolist()

        # 分析类别分布
        class_counts = Counter(y)
        print("类别分布:", dict(class_counts))

        # 计算类别权重
        classes = np.unique(y)
        class_weights = compute_class_weight('balanced', classes=classes, y=y)
        class_weight_dict = dict(zip(classes, class_weights))

        # 定义评分函数
        f1_scorer = make_scorer(f1_score, average='macro')

        # 定义模型
        models = {
            'xgb': xgb.XGBClassifier(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            'lgb': lgb.LGBMClassifier(
                n_estimators=300,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            'rf': RandomForestClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            )
        }

        # 交叉验证评估
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        best_score = 0
        best_model_name = None

        for name, model in models.items():
            scores = []
            for train_idx, val_idx in cv.split(X_scaled, y):
                X_train, X_val = X_scaled[train_idx], X_scaled[val_idx]
                y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]

                model.fit(X_train, y_train)
                y_pred = model.predict(X_val)
                score = f1_score(y_val, y_pred, average='macro')
                scores.append(score)

            avg_score = np.mean(scores)
            print(f"{name}: F1-macro = {avg_score:.4f} (+/- {np.std(scores)*2:.4f})")

            if avg_score > best_score:
                best_score = avg_score
                best_model_name = name

        # 训练最佳模型
        print(f"\n最佳模型: {best_model_name}")
        self.best_model = models[best_model_name]
        self.best_model.fit(X_scaled, y)

        return best_score

    def run_training(self):
        """运行完整训练流程"""
        print("=== 优化的5G日志异常检测系统 ===")

        # 1. 加载数据
        if not self.load_and_preprocess_data():
            print("数据加载失败！")
            return None

        # 2. 提取特征
        features_df = self.extract_advanced_features()

        # 3. 训练模型
        best_score = self.train_optimized_models(features_df)

        print(f"\n训练完成！最佳F1-macro分数: {best_score:.4f}")

        return {
            'best_score': best_score,
            'model': self.best_model,
            'scaler': self.scaler,
            'vectorizer': self.vectorizer,
            'feature_columns': self.feature_columns
        }

    def predict(self, log_content):
        """预测单个日志"""
        if self.best_model is None:
            raise ValueError("模型未训练！")

        # 提取特征
        features = {}

        # 基础统计特征
        features['log_length'] = len(log_content)
        features['line_count'] = log_content.count('\n') + 1
        features['word_count'] = len(log_content.split())
        features['char_diversity'] = len(set(log_content)) / max(len(log_content), 1)

        # 时间特征
        time_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})'
        time_matches = re.findall(time_pattern, log_content)
        features['time_entries'] = len(time_matches)

        if time_matches:
            try:
                times = [datetime.fromisoformat(t) for t in time_matches]
                if len(times) > 1:
                    time_span = (max(times) - min(times)).total_seconds()
                    features['time_span'] = time_span
                    features['log_rate'] = len(times) / max(time_span, 1)
                else:
                    features['time_span'] = 0
                    features['log_rate'] = 0
            except:
                features['time_span'] = 0
                features['log_rate'] = 0
        else:
            features['time_span'] = 0
            features['log_rate'] = 0

        # 其他特征...
        features['error_count'] = len(re.findall(r'\[ERROR\]', log_content))
        features['warn_count'] = len(re.findall(r'\[WARN\]', log_content))
        features['info_count'] = len(re.findall(r'\[INFO\]', log_content))
        features['debug_count'] = len(re.findall(r'\[DEBU\]', log_content))

        modules = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF']
        for module in modules:
            features[f'{module.lower()}_count'] = len(re.findall(rf'\[{module}\]', log_content))

        http_codes = re.findall(r'\|\s*(\d{3})\s*\|', log_content)
        features['http_2xx'] = sum(1 for code in http_codes if code.startswith('2'))
        features['http_4xx'] = sum(1 for code in http_codes if code.startswith('4'))
        features['http_5xx'] = sum(1 for code in http_codes if code.startswith('5'))

        error_keywords = ['failed', 'error', 'timeout', 'invalid', 'denied', 'exception']
        features['error_keywords'] = sum(log_content.lower().count(kw) for kw in error_keywords)

        features['unique_ips'] = len(set(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', log_content)))
        features['unique_imsi'] = len(set(re.findall(r'imsi-(\d+)', log_content)))
        features['unique_sessions'] = len(set(re.findall(r'urn:uuid:[\w-]+', log_content)))

        # TF-IDF特征
        tfidf_features = self.vectorizer.transform([log_content])
        tfidf_dict = {f'tfidf_{i}': tfidf_features[0, i]
                     for i in range(tfidf_features.shape[1])}
        features.update(tfidf_dict)

        # 转换为DataFrame
        feature_df = pd.DataFrame([features])

        # 确保所有特征列都存在
        for col in self.feature_columns:
            if col not in feature_df.columns:
                feature_df[col] = 0

        # 选择和排序特征
        X = feature_df[self.feature_columns].fillna(0)

        # 标准化
        X_scaled = self.scaler.transform(X)

        # 预测
        prediction = self.best_model.predict(X_scaled)[0]

        return prediction

def main():
    """主函数"""
    detector = OptimizedLogDetector()

    # 训练模型
    results = detector.run_training()

    if results is None:
        return

    # 保存模型
    import pickle
    with open('optimized_log_model.pkl', 'wb') as f:
        pickle.dump({
            'model': detector.best_model,
            'scaler': detector.scaler,
            'vectorizer': detector.vectorizer,
            'feature_columns': detector.feature_columns
        }, f)

    print("模型已保存到 optimized_log_model.pkl")

    # 示例预测
    if detector.logs_data:
        sample_log = detector.logs_data[0]['content']
        prediction = detector.predict(sample_log)
        actual = detector.logs_data[0]['fault_type']
        print(f"\n示例预测:")
        print(f"实际标签: {actual}")
        print(f"预测标签: {prediction}")

if __name__ == "__main__":
    main()