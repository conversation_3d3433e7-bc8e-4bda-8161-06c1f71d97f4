#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成提交格式的预测结果
用于测试集预测和结果提交
"""

import os
import json
import re
import pandas as pd
import numpy as np
import pickle
from datetime import datetime

class SubmissionPredictor:
    """提交预测器"""

    def __init__(self, model_path='final_log_model.pkl'):
        self.model_path = model_path
        self.model_data = None
        self.load_model()

    def load_model(self):
        """加载训练好的模型"""
        try:
            with open(self.model_path, 'rb') as f:
                self.model_data = pickle.load(f)
            print(f"模型加载成功: {self.model_path}")
        except Exception as e:
            print(f"模型加载失败: {e}")
            raise

    def extract_features(self, content):
        """提取特征（与训练时保持一致）"""
        features = {}

        # 基础统计特征
        features['log_length'] = len(content)
        features['line_count'] = content.count('\n') + 1
        features['word_count'] = len(content.split())

        # 日志级别计数
        features['error_count'] = content.count('[ERROR]')
        features['warn_count'] = content.count('[WARN]')
        features['info_count'] = content.count('[INFO]')
        features['debug_count'] = content.count('[DEBU]')

        # 网络模块计数
        modules = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF']
        for module in modules:
            features[f'{module.lower()}_count'] = content.count(f'[{module}]')

        # HTTP状态码
        features['http_200_count'] = content.count('| 200 |')
        features['http_201_count'] = content.count('| 201 |')
        features['http_204_count'] = content.count('| 204 |')
        features['http_400_count'] = content.count('| 400 |')
        features['http_404_count'] = content.count('| 404 |')
        features['http_500_count'] = content.count('| 500 |')

        # 关键词计数
        features['failed_count'] = content.lower().count('failed')
        features['error_word_count'] = content.lower().count('error')
        features['timeout_count'] = content.lower().count('timeout')
        features['success_count'] = content.lower().count('success')
        features['complete_count'] = content.lower().count('complete')

        # 时间相关
        time_matches = re.findall(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', content)
        features['time_entries'] = len(time_matches)

        # IP地址和标识符
        features['ip_count'] = len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content))
        features['imsi_count'] = len(re.findall(r'imsi-\d+', content))
        features['uuid_count'] = len(re.findall(r'urn:uuid:', content))

        return features

    def predict_single(self, log_content):
        """预测单个日志"""
        # 提取结构化特征
        struct_features = self.extract_features(log_content)

        # 提取文本特征
        text_sample = log_content[:10000]  # 与训练时保持一致
        tfidf_features = self.model_data['vectorizer'].transform([text_sample])

        # 合并特征
        struct_array = np.array([list(struct_features.values())])
        text_array = tfidf_features.toarray()
        X = np.hstack([struct_array, text_array])

        # 标准化
        X_scaled = self.model_data['scaler'].transform(X)

        # 预测
        prediction = self.model_data['model'].predict(X_scaled)[0]

        return prediction

    def predict_test_set(self, test_dir='files/test'):
        """预测测试集"""
        print(f"开始预测测试集: {test_dir}")

        if not os.path.exists(test_dir):
            print(f"测试目录不存在: {test_dir}")
            return None

        # 获取测试文件列表
        test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
        test_files.sort(key=lambda x: int(x.split('.')[0]))

        print(f"找到 {len(test_files)} 个测试文件")

        results = []

        for i, test_file in enumerate(test_files):
            if i % 10 == 0:
                print(f"预测进度: {i+1}/{len(test_files)}")

            file_id = int(test_file.split('.')[0])
            file_path = os.path.join(test_dir, test_file)

            try:
                # 读取文件
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()

                # 解析JSON
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        log_content = json_data.get('content', '')
                    except:
                        log_content = content
                else:
                    log_content = content

                # 预测
                prediction = self.predict_single(log_content)

                results.append({
                    '日志片段文件编号': file_id,
                    '故障类型': int(prediction)
                })

            except Exception as e:
                print(f"预测文件 {test_file} 失败: {e}")
                # 默认预测为0（正常）
                results.append({
                    '日志片段文件编号': file_id,
                    '故障类型': 0
                })

        return results

    def save_submission(self, results, filename='submission.csv'):
        """保存提交文件"""
        if results is None:
            print("没有预测结果可保存")
            return

        df = pd.DataFrame(results)
        df = df.sort_values('日志片段文件编号')
        df.to_csv(filename, index=False, encoding='utf-8-sig')

        print(f"提交文件已保存: {filename}")
        print(f"预测结果数量: {len(df)}")

        # 显示预测分布
        pred_dist = df['故障类型'].value_counts().sort_index()
        print("预测分布:")
        for fault_type, count in pred_dist.items():
            percentage = (count / len(df)) * 100
            print(f"  故障类型 {fault_type}: {count} 个 ({percentage:.1f}%)")

def main():
    """主函数"""
    print("=== 5G日志异常检测 - 提交预测 ===")

    # 检查模型文件
    if not os.path.exists('final_log_model.pkl'):
        print("错误: 找不到模型文件 'final_log_model.pkl'")
        print("请先运行 'python final_solution.py' 训练模型")
        return

    # 创建预测器
    predictor = SubmissionPredictor()

    # 预测测试集
    results = predictor.predict_test_set()

    if results:
        # 保存提交文件
        predictor.save_submission(results)
        print("\n预测完成！")
    else:
        print("预测失败！")

if __name__ == "__main__":
    main()