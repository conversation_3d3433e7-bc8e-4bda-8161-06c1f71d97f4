# ============================================================================
# 改进的日志异常检测系统 - 针对5G核心网日志分析
# ============================================================================

import subprocess
import sys
import warnings

warnings.filterwarnings('ignore')


def install_packages(packages):
    """安装指定的Python包"""
    for package in packages:
        try:
            __import__(package.split('==')[0] if '==' in package else package)
            print(f"{package} 已安装，跳过安装")
        except ImportError:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])


# 需要安装的库
required_packages = [
    "pandas",
    "numpy",
    "scikit-learn",
    "xgboost",
    "imbalanced-learn",  # 处理不平衡数据
    "wordcloud",  # 可选：文本分析
    "seaborn",  # 可选：可视化
    "matplotlib"  # 可选：可视化
]

install_packages(required_packages)

# 导入库
import re
import os
import pickle
from collections import Counter, defaultdict
from datetime import datetime
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, f1_score
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.decomposition import TruncatedSVD
from imblearn.over_sampling import SMOTE, ADASYN
from imblearn.under_sampling import EditedNearestNeighbours
from imblearn.combine import SMOTEENN
import xgboost as xgb

print("所有库已成功导入！")


# ============================================================================
# 数据加载器
# ============================================================================

class LogDataLoader:
    def __init__(self, train_dir='files/train', test_dir='files/test'):
        self.train_dir = train_dir
        self.test_dir = test_dir

    def load_single_log_file(self, file_path):
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()

                    # 检查是否是JSON格式
                    if content.startswith('{') and content.endswith('}'):
                        try:
                            import json
                            json_data = json.loads(content)
                            # 提取实际的日志内容
                            if 'content' in json_data:
                                return json_data['content']
                            else:
                                return content
                        except json.JSONDecodeError:
                            return content
                    else:
                        return content

                except UnicodeDecodeError:
                    continue

            # 如果所有编码都失败，使用错误忽略模式
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()

            # 再次尝试JSON解析
            if content.startswith('{'):
                try:
                    import json
                    json_data = json.loads(content)
                    if 'content' in json_data:
                        return json_data['content']
                except:
                    pass

            return content
        except Exception as e:
            print(f"Error loading {file_path}: {e}")
            return ""

    def load_train_data(self):
        label_file = os.path.join(self.train_dir, 'label.csv')
        if not os.path.exists(label_file):
            raise FileNotFoundError(f"Label file not found: {label_file}")

        # 尝试多种编码读取标签文件
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
        labels_df = None

        for encoding in encodings:
            try:
                labels_df = pd.read_csv(label_file, encoding=encoding)
                print(f"成功使用 {encoding} 编码加载标签文件: {len(labels_df)} 条记录")
                break
            except UnicodeDecodeError:
                continue

        if labels_df is None:
            # 最后尝试忽略错误
            labels_df = pd.read_csv(label_file, encoding='utf-8', errors='ignore')
            print(f"使用错误忽略模式加载标签文件: {len(labels_df)} 条记录")

        log_data = []
        log_ids = []

        for _, row in labels_df.iterrows():
            log_id = int(row['日志片段文件编号'])
            log_file = os.path.join(self.train_dir, f'{log_id}.txt')

            if os.path.exists(log_file):
                content = self.load_single_log_file(log_file)
                log_data.append(content)
                log_ids.append(log_id)
            else:
                print(f"Warning: Log file {log_file} not found")

        print(f"成功加载 {len(log_data)} 个训练日志文件")
        return log_data, labels_df['故障类型'].tolist(), log_ids

    def load_test_data(self):
        log_data = []
        log_ids = []

        if not os.path.exists(self.test_dir):
            raise FileNotFoundError(f"Test directory not found: {self.test_dir}")

        test_files = [f for f in os.listdir(self.test_dir) if f.endswith('.txt')]
        test_files.sort(key=lambda x: int(x.split('.')[0]))

        for file_name in test_files:
            log_id = int(file_name.split('.')[0])
            file_path = os.path.join(self.test_dir, file_name)
            content = self.load_single_log_file(file_path)
            log_data.append(content)
            log_ids.append(log_id)

        print(f"成功加载 {len(log_data)} 个测试日志文件")
        return log_data, log_ids


# ============================================================================
# 高级特征提取器
# ============================================================================

class AdvancedLogFeatureExtractor:
    def __init__(self):
        self.tfidf_vectorizer = None
        self.scaler = RobustScaler()  # 使用RobustScaler，对异常值更稳健
        self.svd = TruncatedSVD(n_components=100, random_state=42)
        self.fitted = False

        # 5G核心网关键词词典
        self.core_network_keywords = [
            'AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
            'NGAP', 'HTTP', 'SBI', 'PDU', 'Session', 'UE', 'SUPI', 'PLMN', 'DNN',
            'QoS', 'Slice', 'Registration', 'Authentication', 'Authorization'
        ]

        # 错误关键词
        self.error_keywords = [
            'failed', 'error', 'timeout', 'exception', 'invalid', 'null', 'denied',
            'reject', 'abort', 'disconnect', 'unreachable', 'overflow', 'conflict'
        ]

    def extract_advanced_statistical_features(self, log_content):
        """提取高级统计特征"""
        if not log_content:
            return np.zeros(35)

        lines = [line.strip() for line in log_content.split('\n') if line.strip()]
        features = []

        # 基本统计
        features.append(len(lines))  # 日志行数
        features.append(len(log_content))  # 总字符数
        features.append(len(log_content.split()))  # 总词数

        if lines:
            line_lengths = [len(line) for line in lines]
            features.extend([
                np.mean(line_lengths),  # 平均行长度
                np.std(line_lengths) if len(line_lengths) > 1 else 0,  # 行长度标准差
                np.median(line_lengths),  # 行长度中位数
                np.percentile(line_lengths, 75) - np.percentile(line_lengths, 25),  # IQR
                max(line_lengths),  # 最大行长度
                min(line_lengths)  # 最小行长度
            ])
        else:
            features.extend([0] * 6)

        # 时间特征
        timestamps = self._extract_timestamps(lines)
        if len(timestamps) > 1:
            time_diffs = np.diff(timestamps)
            features.extend([
                np.mean(time_diffs),  # 平均时间间隔
                np.std(time_diffs) if len(time_diffs) > 1 else 0,  # 时间间隔标准差
                np.median(time_diffs),  # 时间间隔中位数
                len(time_diffs[time_diffs > np.percentile(time_diffs, 95)]),  # 异常长间隔数
            ])
        else:
            features.extend([0] * 4)

        # 日志级别特征
        level_counts = self._count_log_levels(lines)
        total_lines = len(lines)
        if total_lines > 0:
            features.extend([
                level_counts.get('INFO', 0) / total_lines,
                level_counts.get('ERROR', 0) / total_lines,
                level_counts.get('WARN', 0) / total_lines,
                level_counts.get('DEBUG', 0) / total_lines,
                level_counts.get('TRACE', 0) / total_lines
            ])
        else:
            features.extend([0] * 5)

        # 5G核心网模块特征
        module_counts = self._count_5g_modules(lines)
        unique_modules = len(module_counts)
        features.append(unique_modules)  # 唯一模块数

        # 核心网关键词密度
        text_lower = log_content.lower()
        for keyword in self.core_network_keywords[:5]:  # 前5个最重要的
            features.append(text_lower.count(keyword.lower()) / len(lines) if lines else 0)

        # 错误模式特征
        for keyword in self.error_keywords[:5]:  # 前5个最重要的错误词
            features.append(text_lower.count(keyword) / len(lines) if lines else 0)

        # 网络连接特征
        features.append(len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', log_content)))  # IP数量
        features.append(len(re.findall(r':\d{2,5}\b', log_content)))  # 端口数量
        features.append(len(re.findall(r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}',
                                       log_content)))  # UUID数量

        # 重复和唯一性特征
        features.append(len(lines) - len(set(lines)) if lines else 0)  # 重复行数
        features.append(len(set(lines)) / len(lines) if lines else 0)  # 唯一行比例

        return np.array(features)

    def _extract_timestamps(self, lines):
        """提取时间戳"""
        timestamps = []
        for line in lines:
            # 匹配多种时间格式
            patterns = [
                r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',
                r'(\d{2}:\d{2}:\d{2})',
                r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
            ]

            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    try:
                        if 'T' in match.group(1):
                            ts = datetime.strptime(match.group(1), '%Y-%m-%dT%H:%M:%S')
                        elif '-' in match.group(1) and ' ' in match.group(1):
                            ts = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                        else:
                            # 只有时间，使用固定日期
                            ts = datetime.strptime(f"2024-01-01 {match.group(1)}", '%Y-%m-%d %H:%M:%S')
                        timestamps.append(ts.timestamp())
                        break
                    except:
                        continue
        return sorted(timestamps)

    def _count_log_levels(self, lines):
        """统计日志级别"""
        levels = defaultdict(int)
        for line in lines:
            line_upper = line.upper()
            if '[INFO]' in line_upper:
                levels['INFO'] += 1
            elif '[ERROR]' in line_upper or '[ERR]' in line_upper:
                levels['ERROR'] += 1
            elif '[WARN]' in line_upper or '[WARNING]' in line_upper:
                levels['WARN'] += 1
            elif '[DEBUG]' in line_upper:
                levels['DEBUG'] += 1
            elif '[TRACE]' in line_upper:
                levels['TRACE'] += 1
        return levels

    def _count_5g_modules(self, lines):
        """统计5G核心网模块"""
        modules = defaultdict(int)
        for line in lines:
            for module in self.core_network_keywords:
                if f'[{module}]' in line.upper():
                    modules[module] += 1
        return modules

    def extract_advanced_text_features(self, log_contents, max_features=2000):
        """提取高级文本特征"""
        # 预处理文本
        processed_texts = []
        for content in log_contents:
            # 保留更多信息，只去除时间戳
            text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIMESTAMP>', content)
            text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)  # IP地址替换
            text = re.sub(r':\d{2,5}\b', ':<PORT>', text)  # 端口号替换
            text = re.sub(r'[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}', '<UUID>',
                          text)  # UUID替换
            text = re.sub(r'\s+', ' ', text).strip()
            processed_texts.append(text)

        # 使用更高级的TF-IDF配置
        if self.tfidf_vectorizer is None:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=max_features,
                ngram_range=(1, 3),  # 1-3gram
                min_df=2,
                max_df=0.9,
                sublinear_tf=True,  # 使用对数TF
                analyzer='word',
                token_pattern=r'\b\w+\b'
            )
            tfidf_features = self.tfidf_vectorizer.fit_transform(processed_texts)
        else:
            tfidf_features = self.tfidf_vectorizer.transform(processed_texts)

        # 使用SVD降维
        if not self.fitted:
            tfidf_reduced = self.svd.fit_transform(tfidf_features)
            self.fitted = True
        else:
            tfidf_reduced = self.svd.transform(tfidf_features)

        return tfidf_reduced

    def extract_sequence_features(self, log_contents):
        """提取序列特征"""
        sequence_features = []

        for content in log_contents:
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            features = []

            # 错误序列模式
            error_sequence = []
            info_sequence = []

            for line in lines:
                if '[ERROR]' in line.upper():
                    error_sequence.append(1)
                    info_sequence.append(0)
                elif '[INFO]' in line.upper():
                    error_sequence.append(0)
                    info_sequence.append(1)
                else:
                    error_sequence.append(0)
                    info_sequence.append(0)

            # 计算序列特征
            if error_sequence:
                # 错误突发性
                error_bursts = 0
                in_burst = False
                for i in range(len(error_sequence)):
                    if error_sequence[i] == 1:
                        if not in_burst:
                            error_bursts += 1
                            in_burst = True
                    else:
                        in_burst = False

                features.extend([
                    sum(error_sequence),  # 错误总数
                    error_bursts,  # 错误突发数
                    np.mean(error_sequence),  # 错误密度
                ])

                # 连续模式长度
                max_consecutive_errors = 0
                current_consecutive = 0
                for val in error_sequence:
                    if val == 1:
                        current_consecutive += 1
                        max_consecutive_errors = max(max_consecutive_errors, current_consecutive)
                    else:
                        current_consecutive = 0

                features.append(max_consecutive_errors)
            else:
                features.extend([0, 0, 0, 0])

            sequence_features.append(features)

        return np.array(sequence_features)

    def extract_all_features(self, log_contents, fit_scaler=True):
        """提取所有特征"""
        print("提取高级统计特征...")
        stat_features = np.array([self.extract_advanced_statistical_features(content)
                                  for content in log_contents])

        print("提取高级文本特征...")
        text_features = self.extract_advanced_text_features(log_contents)

        print("提取序列特征...")
        seq_features = self.extract_sequence_features(log_contents)

        # 合并所有特征
        all_features = np.hstack([stat_features, text_features, seq_features])

        # 标准化
        if fit_scaler:
            all_features = self.scaler.fit_transform(all_features)
        else:
            all_features = self.scaler.transform(all_features)

        print(f"特征提取完成，特征维度: {all_features.shape}")
        return all_features


# ============================================================================
# 高级模型训练器
# ============================================================================

class AdvancedLogAnomalyDetector:
    def __init__(self):
        self.models = {}
        self.best_model = None
        self.ensemble_model = None
        self.feature_extractor = AdvancedLogFeatureExtractor()
        self.label_counts = None

    def handle_class_imbalance(self, X, y, strategy='smoteenn'):
        """处理类别不平衡"""
        print(f"处理类别不平衡，策略: {strategy}")

        # 检查是否有足够的样本进行重采样
        unique_classes, counts = np.unique(y, return_counts=True)
        min_samples = min(counts)

        if min_samples < 3:
            print("样本过少，跳过重采样")
            return X, y

        if strategy == 'smote':
            sampler = SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))
        elif strategy == 'adasyn':
            sampler = ADASYN(random_state=42, n_neighbors=min(3, min_samples - 1))
        elif strategy == 'smoteenn':
            sampler = SMOTEENN(random_state=42, smote=SMOTE(k_neighbors=min(3, min_samples - 1)))
        else:
            return X, y

        try:
            X_resampled, y_resampled = sampler.fit_resample(X, y)
            print(f"重采样前: {Counter(y)}")
            print(f"重采样后: {Counter(y_resampled)}")
            return X_resampled, y_resampled
        except Exception as e:
            print(f"重采样失败: {e}")
            return X, y

    def create_ensemble_models(self):
        """创建集成模型"""
        models = {
            'XGBoost': xgb.XGBClassifier(
                n_estimators=300,
                learning_rate=0.05,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                min_child_weight=3,
                reg_alpha=0.1,
                reg_lambda=1,
                random_state=42,
                eval_metric='mlogloss',
                tree_method='hist'
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'ExtraTrees': ExtraTreesClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'LogisticRegression': LogisticRegression(
                max_iter=2000,
                random_state=42,
                class_weight='balanced',
                C=0.1,
                solver='liblinear'
            )
        }
        return models

    def train_with_cross_validation(self, X, y, cv_folds=5):
        """使用交叉验证训练模型"""
        print("开始交叉验证训练...")

        # 处理类别不平衡
        X_balanced, y_balanced = self.handle_class_imbalance(X, y, strategy='smoteenn')

        # 创建模型
        models = self.create_ensemble_models()

        results = {}
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

        for name, model in models.items():
            print(f"训练 {name}...")
            try:
                # 交叉验证评估
                cv_scores = cross_val_score(
                    model, X_balanced, y_balanced,
                    cv=cv, scoring='f1_macro', n_jobs=-1
                )

                # 训练完整模型
                model.fit(X_balanced, y_balanced)
                self.models[name] = model

                results[name] = {
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'model': model
                }

                print(f"{name} - CV F1 Macro: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

            except Exception as e:
                print(f"训练 {name} 失败: {e}")

        # 选择最佳单模型
        if results:
            best_model_name = max(results, key=lambda x: results[x]['cv_mean'])
            self.best_model = results[best_model_name]['model']
            print(f"\n最佳单模型: {best_model_name}")

            # 创建集成模型
            self.create_voting_ensemble(results, X_balanced, y_balanced)

        return results

    def create_voting_ensemble(self, results, X_balanced, y_balanced):
        """创建投票集成模型"""
        # 选择表现较好的模型进行集成
        good_models = [(name, data['model']) for name, data in results.items()
                       if data['cv_mean'] > np.mean([d['cv_mean'] for d in results.values()]) - 0.02]

        if len(good_models) >= 2:
            self.ensemble_model = VotingClassifier(
                estimators=good_models,
                voting='soft'  # 使用概率投票
            )
            print(f"创建集成模型，包含: {[name for name, _ in good_models]}")
            # 训练集成模型
            try:
                self.ensemble_model.fit(X_balanced, y_balanced)
                print("集成模型训练完成")
            except Exception as e:
                print(f"集成模型训练失败: {e}")
                self.ensemble_model = self.best_model
        else:
            self.ensemble_model = self.best_model
            print("集成模型退化为最佳单模型")

    def predict(self, X):
        """预测"""
        if self.ensemble_model is not None:
            return self.ensemble_model.predict(X)
        elif self.best_model is not None:
            return self.best_model.predict(X)
        else:
            raise ValueError("模型尚未训练")

    def predict_proba(self, X):
        """预测概率"""
        if self.ensemble_model is not None:
            return self.ensemble_model.predict_proba(X)
        elif self.best_model is not None:
            return self.best_model.predict_proba(X)
        else:
            raise ValueError("模型尚未训练")

    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        y_pred = self.predict(X_test)

        f1_macro = f1_score(y_test, y_pred, average='macro')
        f1_weighted = f1_score(y_test, y_pred, average='weighted')

        print("=== 模型评估结果 ===")
        print(f"Macro F1-score: {f1_macro:.4f}")
        print(f"Weighted F1-score: {f1_weighted:.4f}")
        print("\n详细分类报告:")
        print(classification_report(y_test, y_pred))

        return {
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted,
            'predictions': y_pred
        }

    def save_model(self, filepath):
        """保存模型"""
        model_data = {
            'best_model': self.best_model,
            'ensemble_model': self.ensemble_model,
            'feature_extractor': self.feature_extractor,
            'models': self.models,
            'label_counts': self.label_counts
        }
        with open(filepath, 'wb') as f:
            pickle.dump(model_data, f)
        print(f"模型已保存到: {filepath}")

    def load_model(self, filepath):
        """加载模型"""
        with open(filepath, 'rb') as f:
            model_data = pickle.load(f)

        self.best_model = model_data['best_model']
        self.ensemble_model = model_data.get('ensemble_model')
        self.feature_extractor = model_data['feature_extractor']
        self.models = model_data.get('models', {})
        self.label_counts = model_data.get('label_counts')
        print(f"模型已从 {filepath} 加载")


# ============================================================================
# 主执行流程
# ============================================================================

def analyze_data_distribution(labels, log_ids):
    """数据分布分析"""
    print("=== 数据分布分析 ===")
    label_counts = Counter(labels)

    for label, count in sorted(label_counts.items()):
        percentage = count / len(labels) * 100
        print(f"故障类型 {label}: {count} 个样本 ({percentage:.2f}%)")

    print(f"\n总样本数: {len(labels)}")
    print(f"故障类型数: {len(label_counts)}")

    return label_counts


def main_training_pipeline():
    """主训练流程"""
    print("=== 开始高级日志异常检测训练流程 ===\n")

    # 1. 数据加载
    print("步骤1: 加载数据...")
    loader = LogDataLoader()

    try:
        train_logs, train_labels, train_ids = loader.load_train_data()
        print(f"训练数据加载成功: {len(train_logs)} 个样本")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, None

    # 2. 数据分析
    print("\n步骤2: 数据分布分析...")
    label_distribution = analyze_data_distribution(train_labels, train_ids)

    # 3. 特征提取
    print("\n步骤3: 高级特征提取...")
    detector = AdvancedLogAnomalyDetector()
    detector.label_counts = label_distribution

    X = detector.feature_extractor.extract_all_features(train_logs, fit_scaler=True)
    y = np.array(train_labels)

    print(f"特征矩阵形状: {X.shape}")

    # 4. 数据划分
    print("\n步骤4: 划分训练集和验证集...")
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    print(f"训练集: {X_train.shape[0]} 个样本")
    print(f"验证集: {X_val.shape[0]} 个样本")

    # 5. 模型训练
    print("\n步骤5: 训练集成模型...")
    training_results = detector.train_with_cross_validation(X_train, y_train)

    # 6. 模型评估
    print("\n步骤6: 模型评估...")
    eval_results = detector.evaluate_model(X_val, y_val)

    # 7. 保存模型
    print("\n步骤7: 保存模型...")
    detector.save_model('advanced_log_anomaly_model.pkl')

    return detector, eval_results


def main_prediction_pipeline(model_path='advanced_log_anomaly_model.pkl'):
    """主预测流程"""
    print("=== 开始高级日志异常检测预测流程 ===\n")

    # 1. 加载模型
    print("步骤1: 加载训练好的模型...")
    detector = AdvancedLogAnomalyDetector()

    try:
        detector.load_model(model_path)
        print("模型加载成功!")
    except Exception as e:
        print(f"模型加载失败: {e}")
        print("请先运行训练流程")
        return None, None, None

    # 2. 加载测试数据
    print("\n步骤2: 加载测试数据...")
    loader = LogDataLoader()

    try:
        test_logs, test_ids = loader.load_test_data()
        print(f"测试数据加载成功: {len(test_logs)} 个样本")
    except Exception as e:
        print(f"测试数据加载失败: {e}")
        return None, None, None

    # 3. 特征提取
    print("\n步骤3: 提取测试数据特征...")
    X_test = detector.feature_extractor.extract_all_features(test_logs, fit_scaler=False)
    print(f"测试特征矩阵形状: {X_test.shape}")

    # 4. 预测
    print("\n步骤4: 进行预测...")
    predictions = detector.predict(X_test)
    probabilities = detector.predict_proba(X_test)

    # 5. 后处理预测结果
    print("\n步骤5: 后处理预测结果...")
    # 基于概率的置信度调整
    max_probs = np.max(probabilities, axis=1)
    low_confidence_mask = max_probs < 0.6  # 低置信度样本

    # 对低置信度样本，倾向于预测为正常（类别0）
    adjusted_predictions = predictions.copy()
    for i, (pred, max_prob) in enumerate(zip(predictions, max_probs)):
        if max_prob < 0.5 and pred != 0:  # 极低置信度且预测为异常
            adjusted_predictions[i] = 0

    # 6. 生成结果
    print("\n步骤6: 生成最终结果...")
    results_df = pd.DataFrame({
        '日志片段文件编号': test_ids,
        '故障类型': adjusted_predictions.astype(int)
    })

    # 按照文件编号排序
    results_df = results_df.sort_values('日志片段文件编号').reset_index(drop=True)

    # 显示预测结果分布
    pred_distribution = Counter(adjusted_predictions)
    print("\n预测结果分布:")
    for fault_type, count in sorted(pred_distribution.items()):
        percentage = count / len(adjusted_predictions) * 100
        print(f"故障类型 {fault_type}: {count} 个样本 ({percentage:.2f}%)")

    # 与训练数据分布对比
    if detector.label_counts:
        print("\n训练数据分布对比:")
        total_train = sum(detector.label_counts.values())
        for fault_type in sorted(pred_distribution.keys()):
            train_pct = (detector.label_counts.get(fault_type, 0) / total_train) * 100
            test_pct = (pred_distribution[fault_type] / len(adjusted_predictions)) * 100
            print(f"故障类型 {fault_type}: 训练集 {train_pct:.2f}% vs 测试集 {test_pct:.2f}%")

    # 7. 保存结果
    output_file = 'result.csv'
    results_df.to_csv(output_file, index=False, encoding='GB2312')
    print(f"\n预测结果已保存到: {output_file}")

    return results_df, adjusted_predictions, probabilities


# ============================================================================
# 额外的实用函数
# ============================================================================

def quick_test_single_model():
    """快速测试单个最佳模型（资源受限时使用）"""
    print("=== 快速单模型训练模式 ===")

    # 数据加载
    loader = LogDataLoader()
    try:
        train_logs, train_labels, train_ids = loader.load_train_data()
        print(f"训练数据加载成功: {len(train_logs)} 个样本")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None, 0

    # 简化的特征提取器
    print("使用简化特征提取...")
    extractor = AdvancedLogFeatureExtractor()

    # 只提取统计特征，跳过复杂的文本特征
    try:
        print("提取基础统计特征...")
        stat_features = np.array([extractor.extract_advanced_statistical_features(content)
                                  for content in train_logs])

        print("提取简化序列特征...")
        seq_features = extractor.extract_sequence_features(train_logs)

        # 合并特征
        X = np.hstack([stat_features, seq_features])
        print(f"特征矩阵形状: {X.shape}")

        # 标准化
        X = extractor.scaler.fit_transform(X)
        y = np.array(train_labels)

    except Exception as e:
        print(f"特征提取失败: {e}")
        return None, 0

    # 数据划分
    X_train, X_val, y_train, y_val = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )

    # 处理不平衡
    try:
        unique_classes, counts = np.unique(y_train, return_counts=True)
        min_samples = min(counts)

        if min_samples >= 3:
            smote = SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))
            X_train_balanced, y_train_balanced = smote.fit_resample(X_train, y_train)
            print("应用SMOTE重采样")
        else:
            X_train_balanced, y_train_balanced = X_train, y_train
            print("跳过重采样（样本过少）")

    except Exception as e:
        print(f"重采样失败，使用原始数据: {e}")
        X_train_balanced, y_train_balanced = X_train, y_train

    # 只训练XGBoost模型
    model = xgb.XGBClassifier(
        n_estimators=100,  # 减少树的数量
        learning_rate=0.1,
        max_depth=6,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='mlogloss'
    )

    print("训练XGBoost模型...")
    try:
        model.fit(X_train_balanced, y_train_balanced)

        # 评估
        y_pred = model.predict(X_val)
        f1 = f1_score(y_val, y_pred, average='macro')
        print(f"验证集 F1-Macro: {f1:.4f}")

    except Exception as e:
        print(f"模型训练失败: {e}")
        return None, 0

    # 预测测试集
    try:
        test_logs, test_ids = loader.load_test_data()
        print(f"加载测试数据: {len(test_logs)} 个样本")

        # 使用相同的简化特征提取
        test_stat_features = np.array([extractor.extract_advanced_statistical_features(content)
                                       for content in test_logs])
        test_seq_features = extractor.extract_sequence_features(test_logs)
        X_test = np.hstack([test_stat_features, test_seq_features])
        X_test = extractor.scaler.transform(X_test)

        test_pred = model.predict(X_test)

        # 保存结果
        results_df = pd.DataFrame({
            '日志片段文件编号': test_ids,
            '故障类型': test_pred.astype(int)
        })
        results_df = results_df.sort_values('日志片段文件编号').reset_index(drop=True)
        results_df.to_csv('result_quick.csv', index=False, encoding='GB2312')

        print("快速模型预测完成，结果保存为 result_quick.csv")

        # 显示预测结果分布
        pred_distribution = Counter(test_pred)
        print("\n预测结果分布:")
        for fault_type, count in sorted(pred_distribution.items()):
            percentage = count / len(test_pred) * 100
            print(f"故障类型 {fault_type}: {count} 个样本 ({percentage:.2f}%)")

    except Exception as e:
        print(f"测试数据预测失败: {e}")

    return model, f1


def analyze_feature_importance(model_path='advanced_log_anomaly_model.pkl'):
    """分析特征重要性"""
    try:
        detector = AdvancedLogAnomalyDetector()
        detector.load_model(model_path)

        if 'XGBoost' in detector.models:
            model = detector.models['XGBoost']
            importances = model.feature_importances_

            # 获取最重要的20个特征
            top_indices = np.argsort(importances)[-20:]

            print("=== Top 20 特征重要性 ===")
            for i, idx in enumerate(reversed(top_indices)):
                print(f"{i + 1:2d}. 特征 {idx:3d}: {importances[idx]:.4f}")

        else:
            print("未找到XGBoost模型进行特征重要性分析")

    except Exception as e:
        print(f"特征重要性分析失败: {e}")


def cross_validate_performance():
    """更详细的交叉验证性能评估"""
    print("=== 详细交叉验证评估 ===")

    # 加载数据
    loader = LogDataLoader()
    try:
        train_logs, train_labels, train_ids = loader.load_train_data()
        print(f"数据加载成功: {len(train_logs)} 个样本")
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

    # 特征提取
    extractor = AdvancedLogFeatureExtractor()
    X = extractor.extract_all_features(train_logs, fit_scaler=True)
    y = np.array(train_labels)

    # 5折交叉验证
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

    models = {
        'XGBoost': xgb.XGBClassifier(n_estimators=200, learning_rate=0.1, max_depth=8, random_state=42),
        'RandomForest': RandomForestClassifier(n_estimators=200, max_depth=15, random_state=42,
                                               class_weight='balanced'),
        'ExtraTrees': ExtraTreesClassifier(n_estimators=200, max_depth=15, random_state=42, class_weight='balanced')
    }

    results = {}

    for name, model in models.items():
        print(f"\n评估 {name}...")

        fold_scores = []
        for fold, (train_idx, val_idx) in enumerate(cv.split(X, y)):
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]

            # 处理不平衡
            try:
                unique_classes, counts = np.unique(y_train_fold, return_counts=True)
                min_samples = min(counts)

                if min_samples >= 3:
                    smote = SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))
                    X_train_balanced, y_train_balanced = smote.fit_resample(X_train_fold, y_train_fold)
                else:
                    X_train_balanced, y_train_balanced = X_train_fold, y_train_fold

            except Exception as e:
                X_train_balanced, y_train_balanced = X_train_fold, y_train_fold

            # 训练和预测
            model.fit(X_train_balanced, y_train_balanced)
            y_pred = model.predict(X_val_fold)

            # 计算F1分数
            f1 = f1_score(y_val_fold, y_pred, average='macro')
            fold_scores.append(f1)

            print(f"  Fold {fold + 1}: {f1:.4f}")

        mean_score = np.mean(fold_scores)
        std_score = np.std(fold_scores)
        results[name] = {'mean': mean_score, 'std': std_score}

        print(f"  平均: {mean_score:.4f} ± {std_score:.4f}")

    # 找出最佳模型
    if results:
        best_model = max(results, key=lambda x: results[x]['mean'])
        print(f"\n最佳模型: {best_model} ({results[best_model]['mean']:.4f} ± {results[best_model]['std']:.4f})")

    return results


def debug_predictions(model_path='advanced_log_anomaly_model.pkl', sample_ids=[1, 2, 3, 4, 5]):
    """调试预测结果，查看具体样本"""
    try:
        # 加载模型
        detector = AdvancedLogAnomalyDetector()
        detector.load_model(model_path)

        # 加载训练数据用于对比
        loader = LogDataLoader()
        train_logs, train_labels, train_ids = loader.load_train_data()

        print("=== 调试预测结果 ===")

        for sample_id in sample_ids:
            if sample_id in train_ids:
                idx = train_ids.index(sample_id)
                log_content = train_logs[idx]
                true_label = train_labels[idx]

                # 提取特征并预测
                X_sample = detector.feature_extractor.extract_all_features([log_content], fit_scaler=False)
                pred_label = detector.predict(X_sample)[0]
                pred_proba = detector.predict_proba(X_sample)[0]

                print(f"\n样本 {sample_id}:")
                print(f"  真实标签: {true_label}")
                print(f"  预测标签: {pred_label}")
                print(f"  预测概率: {pred_proba}")
                print(f"  最大概率: {max(pred_proba):.4f}")
                print(f"  日志前3行:")
                for i, line in enumerate(log_content.split('\n')[:3]):
                    if line.strip():
                        print(f"    {line}")
            else:
                print(f"样本 {sample_id} 不在训练集中")

    except Exception as e:
        print(f"调试失败: {e}")


def data_cleaning_pipeline():
    """数据清洗和预处理流程"""
    print("=== 数据清洗和预处理 ===")

    try:
        loader = LogDataLoader()
        train_logs, train_labels, train_ids = loader.load_train_data()

        print(f"原始数据: {len(train_logs)} 个样本")

        # 数据分布分析
        analyze_data_distribution(train_labels, train_ids)

        # 检查数据质量
        empty_logs = sum(1 for log in train_logs if not log.strip())
        print(f"空日志数量: {empty_logs}")

        # 保存清洗后的数据（这里只是示例，实际可以做更多清洗）
        print("数据清洗完成！")

    except Exception as e:
        print(f"数据清洗失败: {e}")


def train_with_cleaned_data():
    """使用清洗后的数据训练模型"""
    print("=== 使用清洗数据训练 ===")
    # 这里可以调用主训练流程
    return main_training_pipeline()


def improved_quick_test():
    """改进的快速测试"""
    print("=== 改进快速测试 ===")
    return quick_test_single_model()


def working_quick_test():
    """基于能工作版本的快速测试"""
    print("=== 基于工作版本的快速测试 ===")
    return quick_test_single_model()


def ultra_quick_test():
    """超简化测试模式"""
    print("=== 超简化测试模式 ===")

    try:
        # 最简单的数据加载测试
        loader = LogDataLoader()
        train_logs, train_labels, train_ids = loader.load_train_data()

        print(f"数据加载成功: {len(train_logs)} 个样本")
        print("前3个样本的标签:", train_labels[:3])
        print("前3个样本的ID:", train_ids[:3])

        # 简单特征提取测试
        extractor = AdvancedLogFeatureExtractor()
        if train_logs:
            features = extractor.extract_advanced_statistical_features(train_logs[0])
            print(f"第一个样本特征维度: {len(features)}")

        print("超简化测试完成！")

    except Exception as e:
        print(f"超简化测试失败: {e}")
        import traceback
        traceback.print_exc()


# ============================================================================
# 命令行接口和主执行函数
# ============================================================================

def print_usage():
    """打印使用说明"""
    print("""
=== 日志异常检测系统使用说明 ===

主要功能:
1. 完整训练和预测流程（推荐）
2. 快速单模型训练（资源受限时）
3. 特征重要性分析
4. 详细交叉验证评估
5. 调试预测结果

运行方式:
- 完整流程: main()
- 快速模式: quick_test_single_model()
- 分析模式: 调用相应的分析函数

注意事项:
- 确保数据文件在 files/train 和 files/test 目录下
- 训练需要一定时间，请耐心等待
- 生成的 result.csv 文件可直接提交到竞赛平台
""")


def main():
    """主函数：执行完整的训练和预测流程"""
    print("=" * 60)
    print("开始训练阶段")
    print("=" * 60)

    detector, eval_results = main_training_pipeline()

    if detector is not None:
        print(f"\n训练完成！最佳验证 F1-Macro 分数: {eval_results['f1_macro']:.4f}")

        # 预测阶段
        print("\n" + "=" * 60)
        print("开始预测阶段")
        print("=" * 60)

        results_df, predictions, probabilities = main_prediction_pipeline()

        if results_df is not None:
            print("\n所有流程执行完成！")
            print("预测结果已保存为 result.csv 文件")
            print("注意：文件编码为 GB2312，可直接提交到竞赛平台")

            # 显示一些统计信息
            print(f"\n=== 最终统计信息 ===")
            print(f"训练样本数: {len(detector.label_counts) if detector.label_counts else 'N/A'}")
            print(f"测试样本数: {len(predictions) if predictions is not None else 'N/A'}")
            print(f"验证集 F1-Macro: {eval_results['f1_macro']:.4f}")

            return detector, eval_results, results_df
        else:
            print("预测失败，请检查测试数据")
            return detector, eval_results, None

    else:
        print("训练失败，请检查数据文件是否正确放置在 files/ 目录下")
        return None, None, None


# ============================================================================
# 程序入口点
# ============================================================================

if __name__ == "__main__":
    print("=" * 60)
    print("5G核心网日志异常检测系统")
    print("=" * 60)
    print()

    # 打印使用说明
    print_usage()

    # 询问用户运行模式
    print("\n请选择运行模式:")
    print("1. 数据清洗和预处理（推荐第一步）")
    print("2. 使用清洗数据训练模型")
    print("3. 完整训练和预测流程（原版）")
    print("4. 快速单模型训练（原版）")
    print("5. 改进快速训练模式")
    print("6. 改进的快速模式（基于能工作的版本）")
    print("7. 超简化测试模式（调试用）")
    print("8. 特征重要性分析")
    print("9. 详细交叉验证评估")
    print("0. 直接运行完整流程（自动）")

    try:
        choice = input("\n请输入选项编号 (0-9)，直接回车默认选择1: ").strip()
        if not choice:
            choice = "1"

        if choice == "1":
            data_cleaning_pipeline()
        elif choice == "2":
            train_with_cleaned_data()
        elif choice == "3":
            main()
        elif choice == "4":
            quick_test_single_model()
        elif choice == "5":
            improved_quick_test()
        elif choice == "6":
            working_quick_test()
        elif choice == "7":
            ultra_quick_test()
        elif choice == "8":
            analyze_feature_importance()
        elif choice == "9":
            cross_validate_performance()
        elif choice == "0":
            print("\n自动运行完整流程...")
            main()
        else:
            print("无效选择，开始数据清洗...")
            data_cleaning_pipeline()

    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行出错: {e}")
        print("尝试运行快速单模型训练...")
        quick_test_single_model()

print("\n代码加载完成！")