#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保守版解决方案 - 基于mytrain.py思路但更加保守，避免过拟合
专注于最稳定的特征和模型配置
"""

import os
import json
import re
import pandas as pd
import numpy as np
from collections import Counter
import warnings
import gc
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import f1_score, classification_report

try:
    from imblearn.over_sampling import SMOTE
    IMBALANCED_AVAILABLE = True
except ImportError:
    IMBALANCED_AVAILABLE = False

import xgboost as xgb

class ConservativeDetector:
    """保守版检测器 - 避免过拟合"""
    
    def __init__(self):
        self.best_model = None
        self.scaler = StandardScaler()
        self.vectorizer = None
        self.label_counts = None
        
        # 精选关键词
        self.core_keywords = ['AMF', 'SMF', 'UPF', 'HTTP', 'ERROR', 'WARN', 'INFO']
        self.error_keywords = ['failed', 'error', 'timeout', 'invalid', 'denied']
    
    def load_data_efficiently(self):
        """高效数据加载"""
        print("步骤1: 加载数据...")
        
        # 加载标签
        label_file = 'files/train/label.csv'
        labels_df = None
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                labels_df = pd.read_csv(label_file, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if labels_df is None:
            raise ValueError("无法读取标签文件")
        
        print(f"标签数据: {len(labels_df)} 条")
        
        # 分析类别分布
        label_counts = Counter(labels_df['故障类型'])
        self.label_counts = label_counts
        print("类别分布:")
        for label, count in sorted(label_counts.items()):
            percentage = count / len(labels_df) * 100
            print(f"  类别 {label}: {count} 样本 ({percentage:.1f}%)")
        
        # 加载日志文件
        log_contents = []
        labels = []
        
        print(f"开始处理 {len(labels_df)} 个日志文件...")
        for i, (_, row) in enumerate(labels_df.iterrows()):
            if i % 20 == 0:
                print(f"  处理进度: {i+1}/{len(labels_df)}")
            
            log_id = int(row['日志片段文件编号'])
            log_file = f'files/train/{log_id}.txt'
            
            if os.path.exists(log_file):
                try:
                    content = None
                    for encoding in ['utf-8', 'gbk', 'gb2312']:
                        try:
                            with open(log_file, 'r', encoding=encoding, errors='ignore') as f:
                                content = f.read().strip()
                            break
                        except:
                            continue
                    
                    if content:
                        # 解析JSON格式
                        if content.startswith('{'):
                            try:
                                json_data = json.loads(content)
                                content = json_data.get('content', content)
                            except:
                                pass
                        
                        # 截断过长内容
                        if len(content) > 8000:
                            content = content[:8000]
                        
                        log_contents.append(content)
                        labels.append(int(row['故障类型']))
                    
                except Exception as e:
                    continue
        
        print(f"成功处理 {len(log_contents)} 个样本")
        gc.collect()
        
        return log_contents, labels
    
    def extract_conservative_features(self, log_contents, fit_vectorizer=True):
        """提取保守特征 - 只使用最稳定的特征"""
        print("步骤2: 保守特征提取...")
        
        # 1. 基础统计特征
        struct_features = []
        for content in log_contents:
            features = []
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            
            # 基础统计
            features.extend([
                len(lines),  # 行数
                len(content),  # 字符数
                len(content.split())  # 词数
            ])
            
            # 日志级别统计
            text_upper = content.upper()
            total_lines = max(len(lines), 1)
            features.extend([
                text_upper.count('[ERROR]') / total_lines,
                text_upper.count('[WARN]') / total_lines,
                text_upper.count('[INFO]') / total_lines
            ])
            
            # 关键词统计
            text_lower = content.lower()
            for keyword in self.core_keywords:
                features.append(text_lower.count(keyword.lower()) / total_lines)
            
            for keyword in self.error_keywords:
                features.append(text_lower.count(keyword) / total_lines)
            
            # 网络特征
            features.extend([
                len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content)),  # IP数量
                content.count('| 200 |'),  # HTTP 200
                content.count('| 400 |'),  # HTTP 400
                content.count('| 500 |')   # HTTP 500
            ])
            
            struct_features.append(features)
        
        struct_features = np.array(struct_features)
        
        # 2. 简化的文本特征
        processed_texts = []
        for content in log_contents:
            # 简单预处理
            text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIME>', content)
            text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
            text = re.sub(r'\s+', ' ', text).strip()
            processed_texts.append(text)
        
        if fit_vectorizer:
            self.vectorizer = TfidfVectorizer(
                max_features=200,  # 大幅减少特征数量
                ngram_range=(1, 1),  # 只使用单词
                min_df=3,
                max_df=0.8,
                sublinear_tf=True
            )
            text_features = self.vectorizer.fit_transform(processed_texts).toarray()
        else:
            text_features = self.vectorizer.transform(processed_texts).toarray()
        
        # 合并特征
        all_features = np.hstack([struct_features, text_features])
        
        print(f"保守特征提取完成，特征维度: {all_features.shape}")
        return all_features
    
    def handle_imbalance_conservative(self, X, y):
        """保守的不平衡处理"""
        if not IMBALANCED_AVAILABLE:
            print("使用类别权重处理不平衡")
            return X, y
        
        unique_classes, counts = np.unique(y, return_counts=True)
        min_samples = min(counts)
        
        if min_samples < 3:
            print("样本过少，跳过重采样")
            return X, y
        
        try:
            # 使用更保守的SMOTE参数
            smote = SMOTE(random_state=42, k_neighbors=min(2, min_samples - 1))
            X_resampled, y_resampled = smote.fit_resample(X, y)
            
            # 限制重采样后的样本数量，避免过度合成
            max_samples_per_class = int(np.mean(counts) * 1.5)  # 最多增加50%
            
            final_X = []
            final_y = []
            
            for class_label in unique_classes:
                class_mask = y_resampled == class_label
                class_X = X_resampled[class_mask]
                class_y = y_resampled[class_mask]
                
                if len(class_X) > max_samples_per_class:
                    indices = np.random.choice(len(class_X), max_samples_per_class, replace=False)
                    class_X = class_X[indices]
                    class_y = class_y[indices]
                
                final_X.append(class_X)
                final_y.append(class_y)
            
            X_final = np.vstack(final_X)
            y_final = np.hstack(final_y)
            
            print(f"保守重采样: {Counter(y)} -> {Counter(y_final)}")
            return X_final, y_final
            
        except Exception as e:
            print(f"重采样失败: {e}")
            return X, y
    
    def create_conservative_model(self):
        """创建保守模型"""
        # 使用更保守的参数
        model = xgb.XGBClassifier(
            n_estimators=100,  # 减少树的数量
            learning_rate=0.1,
            max_depth=4,  # 减少深度
            subsample=0.8,
            colsample_bytree=0.8,
            min_child_weight=5,  # 增加最小子节点权重
            reg_alpha=0.1,
            reg_lambda=1,
            random_state=42,
            eval_metric='mlogloss',
            n_jobs=-1
        )
        return model
    
    def train_conservative_model(self, X, y):
        """保守训练"""
        print("步骤3: 保守训练...")
        
        # 处理不平衡
        X_balanced, y_balanced = self.handle_imbalance_conservative(X, y)
        
        # 标准化
        X_balanced = self.scaler.fit_transform(X_balanced)
        
        # 创建模型
        model = self.create_conservative_model()
        
        # 交叉验证
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_scores = cross_val_score(
            model, X_balanced, y_balanced,
            cv=cv, scoring='f1_macro', n_jobs=-1
        )
        
        print(f"交叉验证 F1-macro: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        
        # 训练最终模型
        model.fit(X_balanced, y_balanced)
        self.best_model = model
        
        return cv_scores.mean()
    
    def predict_conservative(self, X):
        """保守预测"""
        X_scaled = self.scaler.transform(X)
        
        if self.best_model is not None:
            predictions = self.best_model.predict(X_scaled)
            try:
                probabilities = self.best_model.predict_proba(X_scaled)
                max_probs = np.max(probabilities, axis=1)
                
                # 更保守的置信度调整
                adjusted_predictions = predictions.copy()
                for i, (pred, max_prob) in enumerate(zip(predictions, max_probs)):
                    if max_prob < 0.6 and pred != 0:  # 提高置信度阈值
                        adjusted_predictions[i] = 0
                
                return adjusted_predictions, probabilities
            except:
                return predictions, None
        else:
            raise ValueError("模型尚未训练")
    
    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        predictions, _ = self.predict_conservative(X_test)
        
        f1_macro = f1_score(y_test, predictions, average='macro')
        f1_weighted = f1_score(y_test, predictions, average='weighted')
        
        print("=== 保守模型评估结果 ===")
        print(f"Macro F1-score: {f1_macro:.4f}")
        print(f"Weighted F1-score: {f1_weighted:.4f}")
        print("\n详细分类报告:")
        print(classification_report(y_test, predictions))
        
        return {
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted,
            'predictions': predictions
        }

    def run_complete_training(self):
        """运行完整训练流程"""
        print("=== 保守版5G日志异常检测系统 ===")
        print("基于mytrain.py思路但更加保守，避免过拟合\n")

        try:
            # 1. 加载数据
            log_contents, labels = self.load_data_efficiently()

            # 2. 特征提取
            X = self.extract_conservative_features(log_contents, fit_vectorizer=True)
            y = np.array(labels)

            print(f"特征矩阵形状: {X.shape}")
            print(f"样本数: {len(y)}")

            # 3. 数据划分
            print("\n步骤3: 划分训练集和验证集...")
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            print(f"训练集: {X_train.shape[0]} 个样本")
            print(f"验证集: {X_val.shape[0]} 个样本")

            # 4. 训练模型
            cv_score = self.train_conservative_model(X_train, y_train)

            # 5. 评估模型
            print("\n步骤4: 模型评估...")
            eval_results = self.evaluate_model(X_val, y_val)

            print(f"\n=== 训练完成 ===")
            print(f"交叉验证F1-macro: {cv_score:.4f}")
            print(f"验证集F1-macro: {eval_results['f1_macro']:.4f}")

            return {
                'cv_score': cv_score,
                'final_score': eval_results['f1_macro'],
                'model': self.best_model,
                'scaler': self.scaler,
                'vectorizer': self.vectorizer
            }

        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def save_model(self, filename='conservative_model.pkl'):
        """保存模型"""
        import pickle

        model_data = {
            'best_model': self.best_model,
            'scaler': self.scaler,
            'vectorizer': self.vectorizer,
            'label_counts': self.label_counts
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"模型已保存到 {filename}")

def main():
    """主函数"""
    print("=== 保守版5G核心网日志异常检测系统 ===")
    print("基于mytrain.py思路的保守优化版本")
    print("特点: 精选特征 + 保守参数 + 避免过拟合\n")

    # 训练阶段
    detector = ConservativeDetector()
    results = detector.run_complete_training()

    if results is None:
        print("训练失败！")
        return

    # 保存模型
    detector.save_model()

    print(f"\n=== 训练总结 ===")
    print(f"交叉验证F1-macro: {results['cv_score']:.4f}")
    print(f"验证集F1-macro: {results['final_score']:.4f}")
    print("模型训练完成并已保存！")

    # 内存清理
    gc.collect()

    print("\n所有流程执行完成！")

if __name__ == "__main__":
    main()
