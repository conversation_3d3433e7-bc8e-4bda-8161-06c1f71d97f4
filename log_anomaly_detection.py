#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5G核心网日志异常检测和故障诊断系统
目标：实现高精度的多分类故障检测，优化Macro F1-score
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关
from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer, CountVectorizer
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier, VotingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, f1_score
from sklearn.utils.class_weight import compute_class_weight
import xgboost as xgb
import lightgbm as lgb

# 深度学习相关
try:
    import torch
    import torch.nn as nn
    from transformers import AutoTokenizer, AutoModel
    DEEP_LEARNING_AVAILABLE = True
except ImportError:
    DEEP_LEARNING_AVAILABLE = False
    print("深度学习库未安装，将使用传统机器学习方法")

class LogAnomalyDetector:
    """日志异常检测主类"""

    def __init__(self, data_dir='files/train'):
        self.data_dir = data_dir
        self.label_file = os.path.join(data_dir, 'label.csv')
        self.logs_data = []
        self.labels = []
        self.features = None
        self.models = {}
        self.vectorizers = {}
        self.scalers = {}

        # 日志模式和关键词
        self.log_patterns = {
            'error_patterns': [
                r'\[ERROR\]', r'failed', r'error', r'exception', r'timeout',
                r'connection.*failed', r'decode.*failed', r'invalid', r'denied'
            ],
            'warning_patterns': [
                r'\[WARN\]', r'warning', r'retry', r'fallback', r'default.*uri'
            ],
            'info_patterns': [
                r'\[INFO\]', r'handle', r'success', r'complete', r'established'
            ],
            'debug_patterns': [
                r'\[DEBU\]', r'debug'
            ]
        }

        # 5G核心网模块
        self.network_modules = [
            'AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF',
            'NRF', 'NEF', 'AF', 'SMSF', 'LMF', 'GMLC', 'SCP'
        ]

    def load_data(self):
        """加载训练数据和标签"""
        print("步骤1: 加载数据...")

        # 加载标签
        try:
            # 尝试不同的编码方式
            for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
                try:
                    labels_df = pd.read_csv(self.label_file, encoding=encoding)
                    break
                except UnicodeDecodeError:
                    continue
            else:
                # 如果所有编码都失败，使用错误处理
                labels_df = pd.read_csv(self.label_file, encoding='utf-8', errors='ignore')

            # 处理列名（可能包含中文或特殊字符）
            labels_df.columns = ['file_id', 'fault_type']
            print(f"加载标签文件: {len(labels_df)} 条记录")

        except Exception as e:
            print(f"加载标签文件失败: {e}")
            return False

        # 加载日志文件
        log_files = [f for f in os.listdir(self.data_dir) if f.endswith('.txt') and f != 'label.csv']
        log_files.sort(key=lambda x: int(x.split('.')[0]))

        loaded_count = 0
        for log_file in log_files:
            file_id = int(log_file.split('.')[0])
            file_path = os.path.join(self.data_dir, log_file)

            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()

                # 解析JSON格式
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        log_content = json_data.get('content', '')
                    except json.JSONDecodeError:
                        log_content = content
                else:
                    log_content = content

                # 获取对应的标签
                label_row = labels_df[labels_df['file_id'] == file_id]
                if not label_row.empty:
                    fault_type = label_row.iloc[0]['fault_type']
                    self.logs_data.append({
                        'file_id': file_id,
                        'content': log_content,
                        'fault_type': fault_type
                    })
                    loaded_count += 1

            except Exception as e:
                print(f"加载文件 {log_file} 失败: {e}")
                continue

        print(f"成功加载 {loaded_count} 个训练日志文件")

        if loaded_count == 0:
            print("没有成功加载任何数据！")
            return False

        print(f"训练数据加载成功: {len(self.logs_data)} 个样本")
        return True

    def analyze_data_distribution(self):
        """分析数据分布"""
        print("\n步骤2: 数据分布分析...")

        fault_types = [item['fault_type'] for item in self.logs_data]
        fault_counter = Counter(fault_types)

        print("=== 数据分布分析 ===")
        total_samples = len(fault_types)
        for fault_type, count in sorted(fault_counter.items()):
            percentage = (count / total_samples) * 100
            print(f"故障类型 {fault_type}: {count} 个样本 ({percentage:.2f}%)")

        print(f"\n总样本数: {total_samples}")
        print(f"故障类型数: {len(fault_counter)}")

        return fault_counter

    def extract_features(self):
        """特征提取"""
        print("\n步骤3: 特征提取...")

        features_list = []

        for item in self.logs_data:
            content = item['content']
            features = self._extract_single_log_features(content)
            features['file_id'] = item['file_id']
            features['fault_type'] = item['fault_type']
            features_list.append(features)

        self.features = pd.DataFrame(features_list)
        print(f"特征提取完成，共提取 {len(self.features.columns)-2} 个特征")

        return self.features

    def _extract_single_log_features(self, content):
        """从单个日志中提取特征"""
        features = {}

        # 基础统计特征
        features['log_length'] = len(content)
        features['line_count'] = content.count('\n') + 1
        features['word_count'] = len(content.split())

        # 时间特征
        time_pattern = r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})'
        time_matches = re.findall(time_pattern, content)
        features['time_entries'] = len(time_matches)

        if time_matches:
            # 时间跨度
            try:
                times = [datetime.fromisoformat(t) for t in time_matches]
                if len(times) > 1:
                    time_span = (max(times) - min(times)).total_seconds()
                    features['time_span_seconds'] = time_span
                    features['log_frequency'] = len(times) / max(time_span, 1)
                else:
                    features['time_span_seconds'] = 0
                    features['log_frequency'] = 0

                # 小时分布
                hours = [t.hour for t in times]
                features['avg_hour'] = np.mean(hours)
                features['hour_std'] = np.std(hours)
            except:
                features['time_span_seconds'] = 0
                features['log_frequency'] = 0
                features['avg_hour'] = 0
                features['hour_std'] = 0
        else:
            features['time_span_seconds'] = 0
            features['log_frequency'] = 0
            features['avg_hour'] = 0
            features['hour_std'] = 0

        # 日志级别统计
        for level, patterns in self.log_patterns.items():
            count = 0
            for pattern in patterns:
                count += len(re.findall(pattern, content, re.IGNORECASE))
            features[f'{level}_count'] = count

        # 网络模块统计
        for module in self.network_modules:
            pattern = rf'\[{module}\]'
            features[f'{module.lower()}_count'] = len(re.findall(pattern, content))

        # HTTP状态码
        http_codes = re.findall(r'\|\s*(\d{3})\s*\|', content)
        features['http_2xx_count'] = sum(1 for code in http_codes if code.startswith('2'))
        features['http_4xx_count'] = sum(1 for code in http_codes if code.startswith('4'))
        features['http_5xx_count'] = sum(1 for code in http_codes if code.startswith('5'))

        # IP地址统计
        ip_pattern = r'\b(?:\d{1,3}\.){3}\d{1,3}\b'
        ip_addresses = re.findall(ip_pattern, content)
        features['unique_ips'] = len(set(ip_addresses))
        features['total_ips'] = len(ip_addresses)

        # 用户标识符
        imsi_pattern = r'imsi-(\d+)'
        imsi_matches = re.findall(imsi_pattern, content)
        features['unique_imsi'] = len(set(imsi_matches))
        features['total_imsi'] = len(imsi_matches)

        # 会话标识符
        session_pattern = r'urn:uuid:[\w-]+'
        session_matches = re.findall(session_pattern, content)
        features['unique_sessions'] = len(set(session_matches))
        features['total_sessions'] = len(session_matches)

        # 错误关键词密度
        error_keywords = ['failed', 'error', 'timeout', 'invalid', 'denied', 'exception']
        error_count = sum(content.lower().count(keyword) for keyword in error_keywords)
        features['error_density'] = error_count / max(features['word_count'], 1)

        # 成功关键词密度
        success_keywords = ['success', 'complete', 'established', 'accepted']
        success_count = sum(content.lower().count(keyword) for keyword in success_keywords)
        features['success_density'] = success_count / max(features['word_count'], 1)

        return features

    def extract_text_features(self):
        """提取文本特征（TF-IDF等）"""
        print("\n步骤4: 文本特征提取...")

        # 准备文本数据
        texts = [item['content'] for item in self.logs_data]

        # TF-IDF特征
        tfidf = TfidfVectorizer(
            max_features=1000,
            ngram_range=(1, 2),
            stop_words=None,
            min_df=2,
            max_df=0.95
        )

        tfidf_features = tfidf.fit_transform(texts)
        self.vectorizers['tfidf'] = tfidf

        # 转换为DataFrame
        tfidf_df = pd.DataFrame(
            tfidf_features.toarray(),
            columns=[f'tfidf_{i}' for i in range(tfidf_features.shape[1])]
        )

        # 合并特征
        if self.features is not None:
            # 确保索引对齐
            tfidf_df.index = self.features.index
            self.features = pd.concat([self.features, tfidf_df], axis=1)
        else:
            self.features = tfidf_df

        print(f"文本特征提取完成，TF-IDF特征数: {tfidf_features.shape[1]}")
        return self.features

    def prepare_training_data(self):
        """准备训练数据"""
        print("\n步骤5: 准备训练数据...")

        # 分离特征和标签
        feature_columns = [col for col in self.features.columns
                          if col not in ['file_id', 'fault_type']]

        X = self.features[feature_columns]
        y = self.features['fault_type']

        # 处理缺失值
        X = X.fillna(0)

        # 标准化数值特征
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        self.scalers['standard'] = scaler

        print(f"训练数据准备完成: {X_scaled.shape[0]} 样本, {X_scaled.shape[1]} 特征")

        return X_scaled, y, feature_columns

    def train_models(self, X, y):
        """训练多个模型"""
        print("\n步骤6: 模型训练...")

        # 计算类别权重
        classes = np.unique(y)
        class_weights = compute_class_weight('balanced', classes=classes, y=y)
        class_weight_dict = dict(zip(classes, class_weights))

        # 定义模型
        models = {
            'random_forest': RandomForestClassifier(
                n_estimators=200,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            ),
            'xgboost': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            'lightgbm': lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            'gradient_boosting': GradientBoostingClassifier(
                n_estimators=200,
                max_depth=8,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            )
        }

        # 训练模型并评估
        cv_scores = {}

        for name, model in models.items():
            print(f"训练 {name}...")

            # 交叉验证
            cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
            scores = cross_val_score(model, X, y, cv=cv, scoring='f1_macro', n_jobs=-1)
            cv_scores[name] = scores

            print(f"{name} CV F1-macro: {scores.mean():.4f} (+/- {scores.std() * 2:.4f})")

            # 训练完整模型
            model.fit(X, y)
            self.models[name] = model

        return cv_scores

    def create_ensemble_model(self, X, y):
        """创建集成模型"""
        print("\n步骤7: 创建集成模型...")

        # 选择最好的几个模型进行集成
        base_models = [
            ('rf', self.models['random_forest']),
            ('xgb', self.models['xgboost']),
            ('lgb', self.models['lightgbm']),
            ('gb', self.models['gradient_boosting'])
        ]

        # 创建投票分类器
        ensemble = VotingClassifier(
            estimators=base_models,
            voting='soft'  # 使用概率投票
        )

        # 训练集成模型
        ensemble.fit(X, y)
        self.models['ensemble'] = ensemble

        # 评估集成模型
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        ensemble_scores = cross_val_score(ensemble, X, y, cv=cv, scoring='f1_macro', n_jobs=-1)

        print(f"集成模型 CV F1-macro: {ensemble_scores.mean():.4f} (+/- {ensemble_scores.std() * 2:.4f})")

        return ensemble_scores

    def evaluate_models(self, X, y):
        """详细评估模型"""
        print("\n步骤8: 模型评估...")

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        results = {}

        for name, model in self.models.items():
            print(f"\n=== {name} 模型评估 ===")

            # 重新训练（在训练集上）
            model.fit(X_train, y_train)

            # 预测
            y_pred = model.predict(X_test)

            # 计算F1分数
            f1_macro = f1_score(y_test, y_pred, average='macro')
            f1_weighted = f1_score(y_test, y_pred, average='weighted')

            results[name] = {
                'f1_macro': f1_macro,
                'f1_weighted': f1_weighted,
                'predictions': y_pred
            }

            print(f"F1-macro: {f1_macro:.4f}")
            print(f"F1-weighted: {f1_weighted:.4f}")

            # 分类报告
            print("\n分类报告:")
            print(classification_report(y_test, y_pred))

        return results, X_test, y_test

    def run_complete_pipeline(self):
        """运行完整的训练流程"""
        print("=== 5G核心网日志异常检测系统 ===")
        print("开始训练流程...\n")

        # 1. 加载数据
        if not self.load_data():
            return None

        # 2. 分析数据分布
        fault_distribution = self.analyze_data_distribution()

        # 3. 提取结构化特征
        self.extract_features()

        # 4. 提取文本特征
        self.extract_text_features()

        # 5. 准备训练数据
        X, y, feature_columns = self.prepare_training_data()

        # 6. 训练基础模型
        cv_scores = self.train_models(X, y)

        # 7. 创建集成模型
        ensemble_scores = self.create_ensemble_model(X, y)

        # 8. 详细评估
        results, X_test, y_test = self.evaluate_models(X, y)

        print("\n=== 训练完成 ===")
        print("最佳模型性能:")
        best_model = max(results.keys(), key=lambda k: results[k]['f1_macro'])
        print(f"最佳模型: {best_model}")
        print(f"F1-macro: {results[best_model]['f1_macro']:.4f}")

        return {
            'models': self.models,
            'results': results,
            'feature_columns': feature_columns,
            'fault_distribution': fault_distribution
        }

    def predict_single_log(self, log_content, model_name='ensemble'):
        """预测单个日志的故障类型"""
        if model_name not in self.models:
            raise ValueError(f"模型 {model_name} 不存在")

        # 提取特征
        features = self._extract_single_log_features(log_content)

        # 提取文本特征
        if 'tfidf' in self.vectorizers:
            tfidf_features = self.vectorizers['tfidf'].transform([log_content])
            tfidf_dict = {f'tfidf_{i}': tfidf_features[0, i]
                         for i in range(tfidf_features.shape[1])}
            features.update(tfidf_dict)

        # 转换为DataFrame并对齐特征
        feature_df = pd.DataFrame([features])

        # 确保所有特征都存在
        for col in self.features.columns:
            if col not in ['file_id', 'fault_type'] and col not in feature_df.columns:
                feature_df[col] = 0

        # 选择特征列
        feature_columns = [col for col in self.features.columns
                          if col not in ['file_id', 'fault_type']]
        X = feature_df[feature_columns].fillna(0)

        # 标准化
        if 'standard' in self.scalers:
            X_scaled = self.scalers['standard'].transform(X)
        else:
            X_scaled = X.values

        # 预测
        prediction = self.models[model_name].predict(X_scaled)[0]

        # 如果模型支持概率预测
        if hasattr(self.models[model_name], 'predict_proba'):
            probabilities = self.models[model_name].predict_proba(X_scaled)[0]
            return prediction, probabilities

        return prediction, None

def main():
    """主函数"""
    # 创建检测器实例
    detector = LogAnomalyDetector()

    # 运行完整训练流程
    results = detector.run_complete_pipeline()

    if results is None:
        print("训练失败！")
        return

    # 保存模型（可选）
    import pickle

    model_data = {
        'models': detector.models,
        'vectorizers': detector.vectorizers,
        'scalers': detector.scalers,
        'features_columns': results['feature_columns']
    }

    with open('log_anomaly_models.pkl', 'wb') as f:
        pickle.dump(model_data, f)

    print("\n模型已保存到 log_anomaly_models.pkl")

    # 示例预测
    if detector.logs_data:
        sample_log = detector.logs_data[0]['content']
        prediction, probabilities = detector.predict_single_log(sample_log)
        print(f"\n示例预测:")
        print(f"预测故障类型: {prediction}")
        if probabilities is not None:
            print(f"预测概率: {probabilities}")

if __name__ == "__main__":
    main()