#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版增强解决方案 - 基于mytrain.py思路但避免过拟合
专注于最有效的特征和模型
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter, defaultdict
import warnings
import gc
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.metrics import f1_score, classification_report
from sklearn.utils.class_weight import compute_class_weight

try:
    from imblearn.over_sampling import SMOTE
    IMBALANCED_AVAILABLE = True
except ImportError:
    IMBALANCED_AVAILABLE = False

import xgboost as xgb
import lightgbm as lgb

class SimplifiedEnhancedDetector:
    """简化版增强检测器"""
    
    def __init__(self):
        self.models = {}
        self.best_model = None
        self.scaler = StandardScaler()
        self.vectorizer = None
        self.label_counts = None
        
        # 5G核心网关键词
        self.core_keywords = [
            'AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF',
            'NGAP', 'HTTP', 'SBI', 'PDU', 'Session', 'UE', 'SUPI', 'PLMN'
        ]
        
        self.error_keywords = [
            'failed', 'error', 'timeout', 'exception', 'invalid', 'denied',
            'reject', 'abort', 'disconnect', 'unreachable', 'conflict'
        ]
    
    def load_data_efficiently(self):
        """高效数据加载"""
        print("步骤1: 加载数据...")
        
        # 加载标签
        label_file = 'files/train/label.csv'
        labels_df = None
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                labels_df = pd.read_csv(label_file, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue
        
        if labels_df is None:
            raise ValueError("无法读取标签文件")
        
        print(f"标签数据: {len(labels_df)} 条")
        
        # 分析类别分布
        label_counts = Counter(labels_df['故障类型'])
        self.label_counts = label_counts
        print("类别分布:")
        for label, count in sorted(label_counts.items()):
            percentage = count / len(labels_df) * 100
            print(f"  类别 {label}: {count} 样本 ({percentage:.1f}%)")
        
        # 加载日志文件
        log_contents = []
        labels = []
        
        print(f"开始处理 {len(labels_df)} 个日志文件...")
        for i, (_, row) in enumerate(labels_df.iterrows()):
            if i % 20 == 0:
                print(f"  处理进度: {i+1}/{len(labels_df)}")
            
            log_id = int(row['日志片段文件编号'])
            log_file = f'files/train/{log_id}.txt'
            
            if os.path.exists(log_file):
                try:
                    content = None
                    for encoding in ['utf-8', 'gbk', 'gb2312']:
                        try:
                            with open(log_file, 'r', encoding=encoding, errors='ignore') as f:
                                content = f.read().strip()
                            break
                        except:
                            continue
                    
                    if content:
                        # 解析JSON格式
                        if content.startswith('{'):
                            try:
                                json_data = json.loads(content)
                                content = json_data.get('content', content)
                            except:
                                pass
                        
                        # 截断过长内容
                        if len(content) > 10000:
                            content = content[:10000]
                        
                        log_contents.append(content)
                        labels.append(int(row['故障类型']))
                    
                except Exception as e:
                    print(f"处理文件 {log_file} 出错: {e}")
                    continue
        
        print(f"成功处理 {len(log_contents)} 个样本")
        gc.collect()
        
        return log_contents, labels
    
    def extract_features(self, log_contents, fit_vectorizer=True):
        """提取特征 - 基于mytrain.py但简化"""
        print("步骤2: 特征提取...")
        
        # 1. 结构化特征
        struct_features = []
        for content in log_contents:
            features = []
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            
            # 基础统计
            features.extend([
                len(lines),  # 行数
                len(content),  # 字符数
                len(content.split()),  # 词数
                len(set(lines)) / max(len(lines), 1)  # 唯一行比例
            ])
            
            # 日志级别统计
            text_upper = content.upper()
            total_lines = max(len(lines), 1)
            features.extend([
                text_upper.count('[ERROR]') / total_lines,
                text_upper.count('[WARN]') / total_lines,
                text_upper.count('[INFO]') / total_lines,
                text_upper.count('[DEBUG]') / total_lines
            ])
            
            # 5G模块统计
            text_lower = content.lower()
            for keyword in self.core_keywords[:8]:  # 前8个最重要的
                features.append(text_lower.count(keyword.lower()) / total_lines)
            
            # 错误关键词统计
            for keyword in self.error_keywords[:6]:  # 前6个最重要的
                features.append(text_lower.count(keyword) / total_lines)
            
            # 网络特征
            features.extend([
                len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content)),  # IP数量
                len(re.findall(r':\d{2,5}\b', content)),  # 端口数量
                len(re.findall(r'[0-9a-fA-F-]{36}', content))  # UUID数量
            ])
            
            # 错误序列特征 - 基于mytrain.py的思路
            error_sequence = []
            for line in lines:
                if '[ERROR]' in line.upper():
                    error_sequence.append(1)
                else:
                    error_sequence.append(0)
            
            if error_sequence:
                # 错误突发性
                error_bursts = 0
                in_burst = False
                for val in error_sequence:
                    if val == 1:
                        if not in_burst:
                            error_bursts += 1
                            in_burst = True
                    else:
                        in_burst = False
                
                # 连续错误长度
                max_consecutive = 0
                current_consecutive = 0
                for val in error_sequence:
                    if val == 1:
                        current_consecutive += 1
                        max_consecutive = max(max_consecutive, current_consecutive)
                    else:
                        current_consecutive = 0
                
                features.extend([
                    sum(error_sequence),  # 错误总数
                    error_bursts,  # 错误突发数
                    max_consecutive  # 最大连续错误
                ])
            else:
                features.extend([0, 0, 0])
            
            struct_features.append(features)
        
        struct_features = np.array(struct_features)
        
        # 2. 文本特征
        processed_texts = []
        for content in log_contents:
            # 预处理 - 基于mytrain.py的策略
            text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIMESTAMP>', content)
            text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
            text = re.sub(r':\d{2,5}\b', ':<PORT>', text)
            text = re.sub(r'[0-9a-fA-F-]{36}', '<UUID>', text)
            text = re.sub(r'\s+', ' ', text).strip()
            processed_texts.append(text)
        
        if fit_vectorizer:
            self.vectorizer = TfidfVectorizer(
                max_features=500,  # 减少特征数量避免过拟合
                ngram_range=(1, 2),
                min_df=2,
                max_df=0.9,
                sublinear_tf=True
            )
            text_features = self.vectorizer.fit_transform(processed_texts).toarray()
        else:
            text_features = self.vectorizer.transform(processed_texts).toarray()
        
        # 合并特征
        all_features = np.hstack([struct_features, text_features])
        
        print(f"特征提取完成，特征维度: {all_features.shape}")
        return all_features
    
    def handle_imbalance(self, X, y):
        """处理类别不平衡"""
        if not IMBALANCED_AVAILABLE:
            print("使用类别权重处理不平衡")
            return X, y
        
        unique_classes, counts = np.unique(y, return_counts=True)
        min_samples = min(counts)
        
        if min_samples < 3:
            print("样本过少，跳过重采样")
            return X, y
        
        try:
            smote = SMOTE(random_state=42, k_neighbors=min(2, min_samples - 1))
            X_resampled, y_resampled = smote.fit_resample(X, y)
            print(f"SMOTE重采样: {Counter(y)} -> {Counter(y_resampled)}")
            return X_resampled, y_resampled
        except Exception as e:
            print(f"重采样失败: {e}")
            return X, y
    
    def create_models(self):
        """创建模型"""
        models = {
            'XGBoost': xgb.XGBClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=6,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                eval_metric='mlogloss',
                n_jobs=-1
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=200,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'LightGBM': lgb.LGBMClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=6,
                subsample=0.8,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced',
                verbose=-1
            )
        }
        return models

    def train_models(self, X, y):
        """训练模型"""
        print("步骤3: 训练模型...")

        # 处理不平衡
        X_balanced, y_balanced = self.handle_imbalance(X, y)

        # 标准化
        X_balanced = self.scaler.fit_transform(X_balanced)

        # 创建模型
        models = self.create_models()

        results = {}
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)

        for name, model in models.items():
            print(f"训练 {name}...")
            try:
                # 交叉验证
                cv_scores = cross_val_score(
                    model, X_balanced, y_balanced,
                    cv=cv, scoring='f1_macro', n_jobs=-1
                )

                # 训练完整模型
                model.fit(X_balanced, y_balanced)
                self.models[name] = model

                results[name] = {
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'model': model
                }

                print(f"{name}: F1-macro = {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

            except Exception as e:
                print(f"训练 {name} 失败: {e}")

        # 选择最佳模型
        if results:
            best_model_name = max(results, key=lambda x: results[x]['cv_mean'])
            self.best_model = results[best_model_name]['model']
            print(f"\n最佳模型: {best_model_name} (F1-macro: {results[best_model_name]['cv_mean']:.4f})")

        return results

    def predict_with_adjustment(self, X):
        """带置信度调整的预测"""
        X_scaled = self.scaler.transform(X)

        if self.best_model is not None:
            predictions = self.best_model.predict(X_scaled)
            try:
                probabilities = self.best_model.predict_proba(X_scaled)
                max_probs = np.max(probabilities, axis=1)

                # 置信度调整 - 基于mytrain.py的策略
                adjusted_predictions = predictions.copy()
                for i, (pred, max_prob) in enumerate(zip(predictions, max_probs)):
                    if max_prob < 0.5 and pred != 0:  # 低置信度且预测为异常
                        adjusted_predictions[i] = 0

                return adjusted_predictions, probabilities
            except:
                return predictions, None
        else:
            raise ValueError("模型尚未训练")

    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        predictions, _ = self.predict_with_adjustment(X_test)

        f1_macro = f1_score(y_test, predictions, average='macro')
        f1_weighted = f1_score(y_test, predictions, average='weighted')

        print("=== 模型评估结果 ===")
        print(f"Macro F1-score: {f1_macro:.4f}")
        print(f"Weighted F1-score: {f1_weighted:.4f}")
        print("\n详细分类报告:")
        print(classification_report(y_test, predictions))

        return {
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted,
            'predictions': predictions
        }

    def run_complete_training(self):
        """运行完整训练流程"""
        print("=== 简化版增强5G日志异常检测系统 ===")
        print("基于mytrain.py思路的优化版本\n")

        try:
            # 1. 加载数据
            log_contents, labels = self.load_data_efficiently()

            # 2. 特征提取
            X = self.extract_features(log_contents, fit_vectorizer=True)
            y = np.array(labels)

            print(f"特征矩阵形状: {X.shape}")
            print(f"样本数: {len(y)}")

            # 3. 数据划分
            print("\n步骤3: 划分训练集和验证集...")
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            print(f"训练集: {X_train.shape[0]} 个样本")
            print(f"验证集: {X_val.shape[0]} 个样本")

            # 4. 训练模型
            training_results = self.train_models(X_train, y_train)

            # 5. 评估模型
            print("\n步骤4: 模型评估...")
            eval_results = self.evaluate_model(X_val, y_val)

            print(f"\n=== 训练完成 ===")
            print(f"最终F1-macro分数: {eval_results['f1_macro']:.4f}")

            return {
                'final_score': eval_results['f1_macro'],
                'training_results': training_results,
                'model': self.best_model,
                'scaler': self.scaler,
                'vectorizer': self.vectorizer
            }

        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def save_model(self, filename='simplified_enhanced_model.pkl'):
        """保存模型"""
        import pickle

        model_data = {
            'best_model': self.best_model,
            'scaler': self.scaler,
            'vectorizer': self.vectorizer,
            'models': self.models,
            'label_counts': self.label_counts
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"模型已保存到 {filename}")

def main():
    """主函数"""
    print("=== 简化版增强5G核心网日志异常检测系统 ===")
    print("基于mytrain.py思路的优化版本")
    print("特点: 精选特征 + 序列分析 + 置信度调整 + 避免过拟合\n")

    # 训练阶段
    detector = SimplifiedEnhancedDetector()
    results = detector.run_complete_training()

    if results is None:
        print("训练失败！")
        return

    # 保存模型
    detector.save_model()

    print(f"\n=== 训练总结 ===")
    print(f"最终F1-macro分数: {results['final_score']:.4f}")
    print("模型训练完成并已保存！")

    # 内存清理
    gc.collect()

    print("\n所有流程执行完成！")

if __name__ == "__main__":
    main()
