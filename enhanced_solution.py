#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于mytrain.py思路的增强版5G核心网日志异常检测解决方案
融合高级特征工程、序列分析、集成学习等先进技术
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter, defaultdict
import warnings
import gc
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.model_selection import StratifiedKFold, cross_val_score, train_test_split
from sklearn.preprocessing import RobustScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, ExtraTreesClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import f1_score, classification_report
from sklearn.decomposition import TruncatedSVD
from sklearn.utils.class_weight import compute_class_weight

# 不平衡数据处理
try:
    from imblearn.over_sampling import SMOTE, ADASYN
    from imblearn.combine import SMOTEENN
    IMBALANCED_AVAILABLE = True
except ImportError:
    IMBALANCED_AVAILABLE = False
    print("imbalanced-learn未安装，将使用类别权重处理")

import xgboost as xgb
import lightgbm as lgb

class EnhancedLogFeatureExtractor:
    """增强版特征提取器 - 基于mytrain.py的思路"""
    
    def __init__(self):
        self.scaler = RobustScaler()  # 对异常值更稳健
        self.tfidf_vectorizer = None
        self.svd = TruncatedSVD(n_components=100, random_state=42)
        self.fitted = False
        
        # 5G核心网专业词典
        self.core_network_keywords = [
            'AMF', 'SMF', 'UPF', 'AUSF', 'UDM', 'UDR', 'PCF', 'NRF', 'NSSF', 'NEF',
            'NGAP', 'HTTP', 'SBI', 'PDU', 'Session', 'UE', 'SUPI', 'PLMN', 'DNN',
            'QoS', 'Slice', 'Registration', 'Authentication', 'Authorization'
        ]
        
        # 错误关键词扩展
        self.error_keywords = [
            'failed', 'error', 'timeout', 'exception', 'invalid', 'null', 'denied',
            'reject', 'abort', 'disconnect', 'unreachable', 'overflow', 'conflict',
            'forbidden', 'unauthorized', 'unavailable', 'expired'
        ]
        
        # 成功关键词
        self.success_keywords = [
            'success', 'complete', 'established', 'accepted', 'confirmed', 
            'registered', 'authenticated', 'authorized', 'connected'
        ]
    
    def extract_advanced_statistical_features(self, log_content):
        """提取高级统计特征"""
        if not log_content:
            return np.zeros(40)  # 增加特征维度
        
        lines = [line.strip() for line in log_content.split('\n') if line.strip()]
        features = []
        
        # 基本统计特征
        features.append(len(lines))  # 日志行数
        features.append(len(log_content))  # 总字符数
        features.append(len(log_content.split()))  # 总词数
        features.append(len(set(log_content)) / max(len(log_content), 1))  # 字符多样性
        
        if lines:
            line_lengths = [len(line) for line in lines]
            features.extend([
                np.mean(line_lengths),  # 平均行长度
                np.std(line_lengths) if len(line_lengths) > 1 else 0,  # 行长度标准差
                np.median(line_lengths),  # 行长度中位数
                np.percentile(line_lengths, 75) - np.percentile(line_lengths, 25),  # IQR
                max(line_lengths),  # 最大行长度
                min(line_lengths),  # 最小行长度
                len(set(lines)) / len(lines)  # 唯一行比例
            ])
        else:
            features.extend([0] * 7)
        
        # 时间特征分析
        timestamps = self._extract_timestamps(lines)
        if len(timestamps) > 1:
            time_diffs = np.diff(timestamps)
            features.extend([
                np.mean(time_diffs),  # 平均时间间隔
                np.std(time_diffs) if len(time_diffs) > 1 else 0,  # 时间间隔标准差
                np.median(time_diffs),  # 时间间隔中位数
                len(time_diffs[time_diffs > np.percentile(time_diffs, 95)]),  # 异常长间隔数
                max(time_diffs),  # 最大时间间隔
                min(time_diffs)   # 最小时间间隔
            ])
        else:
            features.extend([0] * 6)
        
        # 日志级别特征
        level_counts = self._count_log_levels(lines)
        total_lines = len(lines)
        if total_lines > 0:
            features.extend([
                level_counts.get('INFO', 0) / total_lines,
                level_counts.get('ERROR', 0) / total_lines,
                level_counts.get('WARN', 0) / total_lines,
                level_counts.get('DEBUG', 0) / total_lines,
                level_counts.get('TRACE', 0) / total_lines
            ])
            # 错误率相关
            error_rate = level_counts.get('ERROR', 0) / total_lines
            warn_rate = level_counts.get('WARN', 0) / total_lines
            features.append(error_rate + warn_rate)  # 总异常率
        else:
            features.extend([0] * 6)
        
        # 5G核心网模块特征
        module_counts = self._count_5g_modules(lines)
        features.append(len(module_counts))  # 唯一模块数
        
        # 核心网关键词密度
        text_lower = log_content.lower()
        for keyword in self.core_network_keywords[:5]:
            features.append(text_lower.count(keyword.lower()) / total_lines if total_lines else 0)
        
        # 错误关键词密度
        for keyword in self.error_keywords[:5]:
            features.append(text_lower.count(keyword) / total_lines if total_lines else 0)
        
        # 成功关键词密度
        for keyword in self.success_keywords[:3]:
            features.append(text_lower.count(keyword) / total_lines if total_lines else 0)
        
        # 网络连接特征
        features.append(len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', log_content)))  # IP数量
        features.append(len(re.findall(r':\d{2,5}\b', log_content)))  # 端口数量
        features.append(len(re.findall(r'[0-9a-fA-F-]{36}', log_content)))  # UUID数量
        
        return np.array(features)
    
    def _extract_timestamps(self, lines):
        """提取时间戳"""
        timestamps = []
        for line in lines:
            patterns = [
                r'(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})',
                r'(\d{2}:\d{2}:\d{2})',
                r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, line)
                if match:
                    try:
                        if 'T' in match.group(1):
                            ts = datetime.strptime(match.group(1), '%Y-%m-%dT%H:%M:%S')
                        elif '-' in match.group(1) and ' ' in match.group(1):
                            ts = datetime.strptime(match.group(1), '%Y-%m-%d %H:%M:%S')
                        else:
                            ts = datetime.strptime(f"2024-01-01 {match.group(1)}", '%Y-%m-%d %H:%M:%S')
                        timestamps.append(ts.timestamp())
                        break
                    except:
                        continue
        return sorted(timestamps)
    
    def _count_log_levels(self, lines):
        """统计日志级别"""
        levels = defaultdict(int)
        for line in lines:
            line_upper = line.upper()
            if '[INFO]' in line_upper:
                levels['INFO'] += 1
            elif '[ERROR]' in line_upper or '[ERR]' in line_upper:
                levels['ERROR'] += 1
            elif '[WARN]' in line_upper or '[WARNING]' in line_upper:
                levels['WARN'] += 1
            elif '[DEBUG]' in line_upper or '[DEBU]' in line_upper:
                levels['DEBUG'] += 1
            elif '[TRACE]' in line_upper:
                levels['TRACE'] += 1
        return levels
    
    def _count_5g_modules(self, lines):
        """统计5G核心网模块"""
        modules = defaultdict(int)
        for line in lines:
            for module in self.core_network_keywords:
                if f'[{module}]' in line.upper():
                    modules[module] += 1
        return modules

    def extract_sequence_features(self, log_contents):
        """提取序列特征 - 基于mytrain.py的错误突发性分析"""
        sequence_features = []

        for content in log_contents:
            lines = [line.strip() for line in content.split('\n') if line.strip()]
            features = []

            # 错误序列模式
            error_sequence = []
            info_sequence = []
            warn_sequence = []

            for line in lines:
                line_upper = line.upper()
                if '[ERROR]' in line_upper:
                    error_sequence.append(1)
                    info_sequence.append(0)
                    warn_sequence.append(0)
                elif '[WARN]' in line_upper:
                    error_sequence.append(0)
                    info_sequence.append(0)
                    warn_sequence.append(1)
                elif '[INFO]' in line_upper:
                    error_sequence.append(0)
                    info_sequence.append(1)
                    warn_sequence.append(0)
                else:
                    error_sequence.append(0)
                    info_sequence.append(0)
                    warn_sequence.append(0)

            # 计算序列特征
            if error_sequence:
                # 错误突发性分析
                error_bursts = 0
                in_burst = False
                for i in range(len(error_sequence)):
                    if error_sequence[i] == 1:
                        if not in_burst:
                            error_bursts += 1
                            in_burst = True
                    else:
                        in_burst = False

                features.extend([
                    sum(error_sequence),  # 错误总数
                    error_bursts,  # 错误突发数
                    np.mean(error_sequence),  # 错误密度
                    sum(warn_sequence),  # 警告总数
                    np.mean(warn_sequence),  # 警告密度
                ])

                # 连续错误模式长度
                max_consecutive_errors = 0
                current_consecutive = 0
                for val in error_sequence:
                    if val == 1:
                        current_consecutive += 1
                        max_consecutive_errors = max(max_consecutive_errors, current_consecutive)
                    else:
                        current_consecutive = 0

                features.append(max_consecutive_errors)

                # 错误分布模式
                if len(error_sequence) > 10:
                    # 前半部分错误率
                    first_half_errors = np.mean(error_sequence[:len(error_sequence)//2])
                    # 后半部分错误率
                    second_half_errors = np.mean(error_sequence[len(error_sequence)//2:])
                    features.extend([first_half_errors, second_half_errors])
                else:
                    features.extend([0, 0])
            else:
                features.extend([0] * 8)

            sequence_features.append(features)

        return np.array(sequence_features)

    def extract_advanced_text_features(self, log_contents, max_features=1500):
        """提取高级文本特征 - 基于mytrain.py的预处理策略"""
        # 预处理文本
        processed_texts = []
        for content in log_contents:
            # 保留更多信息，只标准化变量部分
            text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIMESTAMP>', content)
            text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
            text = re.sub(r':\d{2,5}\b', ':<PORT>', text)
            text = re.sub(r'[0-9a-fA-F-]{36}', '<UUID>', text)
            text = re.sub(r'\s+', ' ', text).strip()
            processed_texts.append(text)

        # 使用更高级的TF-IDF配置
        if self.tfidf_vectorizer is None:
            self.tfidf_vectorizer = TfidfVectorizer(
                max_features=max_features,
                ngram_range=(1, 3),  # 1-3gram
                min_df=2,
                max_df=0.9,
                sublinear_tf=True,  # 使用对数TF
                analyzer='word',
                token_pattern=r'\b\w+\b'
            )
            tfidf_features = self.tfidf_vectorizer.fit_transform(processed_texts)
        else:
            tfidf_features = self.tfidf_vectorizer.transform(processed_texts)

        # 使用SVD降维
        if not self.fitted:
            tfidf_reduced = self.svd.fit_transform(tfidf_features)
            self.fitted = True
        else:
            tfidf_reduced = self.svd.transform(tfidf_features)

        return tfidf_reduced

    def extract_all_features(self, log_contents, fit_scaler=True):
        """提取所有特征"""
        print("提取高级统计特征...")
        stat_features = np.array([self.extract_advanced_statistical_features(content)
                                  for content in log_contents])

        print("提取序列特征...")
        seq_features = self.extract_sequence_features(log_contents)

        print("提取高级文本特征...")
        text_features = self.extract_advanced_text_features(log_contents)

        # 合并所有特征
        all_features = np.hstack([stat_features, seq_features, text_features])

        # 标准化
        if fit_scaler:
            all_features = self.scaler.fit_transform(all_features)
        else:
            all_features = self.scaler.transform(all_features)

        print(f"特征提取完成，特征维度: {all_features.shape}")
        return all_features

class EnhancedLogAnomalyDetector:
    """增强版异常检测器 - 融合mytrain.py的集成学习思路"""

    def __init__(self):
        self.models = {}
        self.best_model = None
        self.ensemble_model = None
        self.feature_extractor = EnhancedLogFeatureExtractor()
        self.label_counts = None
        self.class_weights = None

    def load_data_efficiently(self):
        """高效数据加载"""
        print("步骤1: 高效加载数据...")

        # 加载标签
        label_file = 'files/train/label.csv'
        if not os.path.exists(label_file):
            raise FileNotFoundError(f"标签文件不存在: {label_file}")

        # 尝试多种编码读取标签文件
        labels_df = None
        for encoding in ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']:
            try:
                labels_df = pd.read_csv(label_file, encoding=encoding)
                break
            except UnicodeDecodeError:
                continue

        if labels_df is None:
            raise ValueError("无法读取标签文件，尝试了多种编码都失败")
        print(f"标签数据: {len(labels_df)} 条")

        # 分析类别分布
        label_counts = Counter(labels_df['故障类型'])
        print("类别分布:")
        for label, count in sorted(label_counts.items()):
            percentage = count / len(labels_df) * 100
            print(f"  类别 {label}: {count} 样本 ({percentage:.1f}%)")

        self.label_counts = label_counts

        # 加载日志文件
        log_contents = []
        labels = []

        print(f"开始处理 {len(labels_df)} 个日志文件...")
        for i, (_, row) in enumerate(labels_df.iterrows()):
            if i % 20 == 0:
                print(f"  处理进度: {i+1}/{len(labels_df)}")

            log_id = int(row['日志片段文件编号'])
            log_file = f'files/train/{log_id}.txt'

            if os.path.exists(log_file):
                try:
                    # 尝试多种编码
                    content = None
                    for encoding in ['utf-8', 'gbk', 'gb2312']:
                        try:
                            with open(log_file, 'r', encoding=encoding, errors='ignore') as f:
                                content = f.read().strip()
                            break
                        except:
                            continue

                    if content:
                        # 解析JSON格式
                        if content.startswith('{'):
                            try:
                                json_data = json.loads(content)
                                content = json_data.get('content', content)
                            except:
                                pass

                        # 截断过长内容以节省内存
                        if len(content) > 15000:
                            content = content[:15000]

                        log_contents.append(content)
                        labels.append(int(row['故障类型']))

                except Exception as e:
                    print(f"处理文件 {log_file} 出错: {e}")
                    continue

        print(f"成功处理 {len(log_contents)} 个样本")

        # 内存清理
        gc.collect()

        return log_contents, labels

    def handle_class_imbalance(self, X, y, strategy='smoteenn'):
        """处理类别不平衡 - 基于mytrain.py的策略"""
        print(f"处理类别不平衡，策略: {strategy}")

        if not IMBALANCED_AVAILABLE:
            print("使用类别权重处理不平衡")
            return X, y

        # 检查是否有足够的样本进行重采样
        unique_classes, counts = np.unique(y, return_counts=True)
        min_samples = min(counts)

        if min_samples < 3:
            print("样本过少，跳过重采样")
            return X, y

        try:
            if strategy == 'smote':
                sampler = SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))
            elif strategy == 'adasyn':
                sampler = ADASYN(random_state=42, n_neighbors=min(3, min_samples - 1))
            elif strategy == 'smoteenn':
                sampler = SMOTEENN(random_state=42,
                                   smote=SMOTE(k_neighbors=min(3, min_samples - 1)))
            else:
                return X, y

            X_resampled, y_resampled = sampler.fit_resample(X, y)
            print(f"重采样前: {Counter(y)}")
            print(f"重采样后: {Counter(y_resampled)}")
            return X_resampled, y_resampled

        except Exception as e:
            print(f"重采样失败: {e}")
            return X, y

    def create_enhanced_models(self):
        """创建增强版模型集合"""
        # 计算类别权重
        if self.label_counts:
            classes = np.array(list(self.label_counts.keys()))
            y_for_weights = [k for k, v in self.label_counts.items() for _ in range(v)]
            weights = compute_class_weight('balanced', classes=classes, y=y_for_weights)
            self.class_weights = dict(zip(classes, weights))

        models = {
            'XGBoost': xgb.XGBClassifier(
                n_estimators=300,
                learning_rate=0.05,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                min_child_weight=3,
                reg_alpha=0.1,
                reg_lambda=1,
                random_state=42,
                eval_metric='mlogloss',
                tree_method='hist',
                n_jobs=-1
            ),
            'LightGBM': lgb.LGBMClassifier(
                n_estimators=300,
                learning_rate=0.05,
                max_depth=8,
                subsample=0.8,
                colsample_bytree=0.8,
                min_child_samples=3,
                reg_alpha=0.1,
                reg_lambda=1,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'RandomForest': RandomForestClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'ExtraTrees': ExtraTreesClassifier(
                n_estimators=300,
                max_depth=15,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features='sqrt',
                bootstrap=True,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            )
        }
        return models

    def train_with_cross_validation(self, X, y, cv_folds=5):
        """使用交叉验证训练模型"""
        print("步骤2: 开始交叉验证训练...")

        # 处理类别不平衡
        X_balanced, y_balanced = self.handle_class_imbalance(X, y, strategy='smoteenn')

        # 创建模型
        models = self.create_enhanced_models()

        results = {}
        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)

        for name, model in models.items():
            print(f"训练 {name}...")
            try:
                # 交叉验证评估
                cv_scores = cross_val_score(
                    model, X_balanced, y_balanced,
                    cv=cv, scoring='f1_macro', n_jobs=-1
                )

                # 训练完整模型
                model.fit(X_balanced, y_balanced)
                self.models[name] = model

                results[name] = {
                    'cv_mean': cv_scores.mean(),
                    'cv_std': cv_scores.std(),
                    'model': model
                }

                print(f"{name}: F1-macro = {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")

            except Exception as e:
                print(f"训练 {name} 失败: {e}")

        # 选择最佳单模型
        if results:
            best_model_name = max(results, key=lambda x: results[x]['cv_mean'])
            self.best_model = results[best_model_name]['model']
            print(f"\n最佳单模型: {best_model_name} (F1-macro: {results[best_model_name]['cv_mean']:.4f})")

            # 创建集成模型
            self.create_voting_ensemble(results, X_balanced, y_balanced)

        return results

    def create_voting_ensemble(self, results, X_balanced, y_balanced):
        """创建投票集成模型 - 基于mytrain.py的策略"""
        print("步骤3: 创建集成模型...")

        # 选择表现较好的模型进行集成
        mean_score = np.mean([d['cv_mean'] for d in results.values()])
        good_models = [(name, data['model']) for name, data in results.items()
                       if data['cv_mean'] > mean_score - 0.02]

        if len(good_models) >= 2:
            self.ensemble_model = VotingClassifier(
                estimators=good_models,
                voting='soft'  # 使用概率投票
            )
            # 训练集成模型
            self.ensemble_model.fit(X_balanced, y_balanced)
            print(f"创建集成模型，包含: {[name for name, _ in good_models]}")
        else:
            self.ensemble_model = self.best_model
            print("集成模型退化为最佳单模型")

    def predict_with_confidence_adjustment(self, X):
        """带置信度调整的预测 - 基于mytrain.py的后处理策略"""
        if self.ensemble_model is not None:
            predictions = self.ensemble_model.predict(X)
            probabilities = self.ensemble_model.predict_proba(X)
        elif self.best_model is not None:
            predictions = self.best_model.predict(X)
            probabilities = self.best_model.predict_proba(X)
        else:
            raise ValueError("模型尚未训练")

        # 基于概率的置信度调整
        max_probs = np.max(probabilities, axis=1)
        low_confidence_mask = max_probs < 0.6

        # 对低置信度样本，倾向于预测为正常（类别0）
        adjusted_predictions = predictions.copy()
        for i, (pred, max_prob) in enumerate(zip(predictions, max_probs)):
            if max_prob < 0.5 and pred != 0:  # 极低置信度且预测为异常
                adjusted_predictions[i] = 0

        return adjusted_predictions, probabilities

    def evaluate_model(self, X_test, y_test):
        """评估模型"""
        predictions, _ = self.predict_with_confidence_adjustment(X_test)

        f1_macro = f1_score(y_test, predictions, average='macro')
        f1_weighted = f1_score(y_test, predictions, average='weighted')

        print("=== 模型评估结果 ===")
        print(f"Macro F1-score: {f1_macro:.4f}")
        print(f"Weighted F1-score: {f1_weighted:.4f}")
        print("\n详细分类报告:")
        print(classification_report(y_test, predictions))

        return {
            'f1_macro': f1_macro,
            'f1_weighted': f1_weighted,
            'predictions': predictions
        }

    def run_complete_training(self):
        """运行完整训练流程"""
        print("=== 增强版5G日志异常检测系统 ===")
        print("基于mytrain.py思路的改进版本\n")

        try:
            # 1. 高效加载数据
            log_contents, labels = self.load_data_efficiently()

            # 2. 特征提取
            print("\n步骤2: 特征提取...")
            X = self.feature_extractor.extract_all_features(log_contents, fit_scaler=True)
            y = np.array(labels)

            print(f"特征矩阵形状: {X.shape}")
            print(f"样本数: {len(y)}")

            # 3. 数据划分
            print("\n步骤3: 划分训练集和验证集...")
            X_train, X_val, y_train, y_val = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )

            print(f"训练集: {X_train.shape[0]} 个样本")
            print(f"验证集: {X_val.shape[0]} 个样本")

            # 4. 模型训练
            print("\n步骤4: 训练集成模型...")
            training_results = self.train_with_cross_validation(X_train, y_train)

            # 5. 模型评估
            print("\n步骤5: 模型评估...")
            eval_results = self.evaluate_model(X_val, y_val)

            print(f"\n=== 训练完成 ===")
            print(f"最终F1-macro分数: {eval_results['f1_macro']:.4f}")

            return {
                'final_score': eval_results['f1_macro'],
                'training_results': training_results,
                'model': self.ensemble_model or self.best_model,
                'feature_extractor': self.feature_extractor
            }

        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def save_model(self, filename='enhanced_log_model.pkl'):
        """保存模型"""
        import pickle

        model_data = {
            'best_model': self.best_model,
            'ensemble_model': self.ensemble_model,
            'feature_extractor': self.feature_extractor,
            'models': self.models,
            'label_counts': self.label_counts,
            'class_weights': self.class_weights
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"模型已保存到 {filename}")

def predict_test_set(model_path='enhanced_log_model.pkl'):
    """预测测试集"""
    import pickle

    print("=== 测试集预测 ===")

    # 加载模型
    try:
        with open(model_path, 'rb') as f:
            model_data = pickle.load(f)

        detector = EnhancedLogAnomalyDetector()
        detector.best_model = model_data['best_model']
        detector.ensemble_model = model_data.get('ensemble_model')
        detector.feature_extractor = model_data['feature_extractor']
        detector.models = model_data.get('models', {})
        detector.label_counts = model_data.get('label_counts')
        detector.class_weights = model_data.get('class_weights')

        print("模型加载成功!")

    except Exception as e:
        print(f"模型加载失败: {e}")
        return None

    # 加载测试数据
    test_dir = 'files/test'
    if not os.path.exists(test_dir):
        print(f"测试目录不存在: {test_dir}")
        return None

    test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
    test_files.sort(key=lambda x: int(x.split('.')[0]))

    print(f"找到 {len(test_files)} 个测试文件")

    # 加载测试日志
    test_contents = []
    test_ids = []

    for test_file in test_files:
        file_id = int(test_file.split('.')[0])
        file_path = os.path.join(test_dir, test_file)

        try:
            # 尝试多种编码
            content = None
            for encoding in ['utf-8', 'gbk', 'gb2312']:
                try:
                    with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
                        content = f.read().strip()
                    break
                except:
                    continue

            if content:
                # 解析JSON格式
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        content = json_data.get('content', content)
                    except:
                        pass

                # 截断过长内容
                if len(content) > 15000:
                    content = content[:15000]

                test_contents.append(content)
                test_ids.append(file_id)

        except Exception as e:
            print(f"加载文件 {test_file} 失败: {e}")
            # 使用空内容作为默认
            test_contents.append("")
            test_ids.append(file_id)

    print(f"成功加载 {len(test_contents)} 个测试样本")

    # 特征提取
    print("提取测试数据特征...")
    X_test = detector.feature_extractor.extract_all_features(test_contents, fit_scaler=False)

    # 预测
    print("进行预测...")
    predictions, probabilities = detector.predict_with_confidence_adjustment(X_test)

    # 生成结果
    results_df = pd.DataFrame({
        '日志片段文件编号': test_ids,
        '故障类型': predictions.astype(int)
    })

    # 按照文件编号排序
    results_df = results_df.sort_values('日志片段文件编号').reset_index(drop=True)

    # 显示预测结果分布
    pred_distribution = Counter(predictions)
    print("\n预测结果分布:")
    for fault_type, count in sorted(pred_distribution.items()):
        percentage = count / len(predictions) * 100
        print(f"故障类型 {fault_type}: {count} 个样本 ({percentage:.2f}%)")

    # 与训练数据分布对比
    if detector.label_counts:
        print("\n训练数据分布对比:")
        total_train = sum(detector.label_counts.values())
        for fault_type in sorted(pred_distribution.keys()):
            train_pct = (detector.label_counts.get(fault_type, 0) / total_train) * 100
            test_pct = (pred_distribution[fault_type] / len(predictions)) * 100
            print(f"故障类型 {fault_type}: 训练集 {train_pct:.2f}% vs 测试集 {test_pct:.2f}%")

    # 保存结果
    output_file = 'enhanced_result.csv'
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n预测结果已保存到: {output_file}")

    return results_df

def main():
    """主函数"""
    print("=== 增强版5G核心网日志异常检测系统 ===")
    print("基于mytrain.py思路的全面改进版本")
    print("特点: 高级特征工程 + 序列分析 + 集成学习 + 置信度调整\n")

    # 训练阶段
    detector = EnhancedLogAnomalyDetector()
    results = detector.run_complete_training()

    if results is None:
        print("训练失败！")
        return

    # 保存模型
    detector.save_model()

    print(f"\n=== 训练总结 ===")
    print(f"最终F1-macro分数: {results['final_score']:.4f}")
    print("模型训练完成并已保存！")

    # 预测测试集（如果存在）
    if os.path.exists('files/test'):
        print("\n开始预测测试集...")
        test_results = predict_test_set()
        if test_results is not None:
            print("测试集预测完成！")
    else:
        print("\n测试集目录不存在，跳过预测")

    # 内存清理
    gc.collect()

    print("\n所有流程执行完成！")

if __name__ == "__main__":
    main()
