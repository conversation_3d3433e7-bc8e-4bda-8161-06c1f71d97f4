#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的5G日志异常检测训练脚本
"""

import os
import json
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, f1_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
import xgboost as xgb
from collections import Counter
import re

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 加载标签
    label_file = 'files/train/label.csv'
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    labels_df = None
    
    for encoding in encodings:
        try:
            labels_df = pd.read_csv(label_file, encoding=encoding)
            print(f"成功使用 {encoding} 编码加载标签文件: {len(labels_df)} 条记录")
            break
        except UnicodeDecodeError:
            continue
    
    if labels_df is None:
        labels_df = pd.read_csv(label_file, encoding='utf-8', errors='ignore')
    
    # 加载日志文件
    logs = []
    labels = []
    
    for _, row in labels_df.iterrows():
        log_id = int(row['日志片段文件编号'])
        label = int(row['故障类型'])
        log_file = f'files/train/{log_id}.txt'
        
        if os.path.exists(log_file):
            # 读取日志文件
            content = ""
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    with open(log_file, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                    break
                except UnicodeDecodeError:
                    continue
            
            # 解析JSON格式
            if content.startswith('{'):
                try:
                    json_data = json.loads(content)
                    if 'content' in json_data:
                        content = json_data['content']
                except:
                    pass
            
            logs.append(content)
            labels.append(label)
    
    print(f"成功加载 {len(logs)} 个样本")
    return logs, labels

def extract_simple_features(logs):
    """提取简单特征"""
    print("提取特征...")
    
    features = []
    for log in logs:
        if not log:
            features.append([0] * 20)
            continue
            
        # 基础统计特征
        feat = []
        feat.append(len(log))  # 日志长度
        feat.append(log.count('\n'))  # 行数
        feat.append(len(log.split()))  # 词数
        
        # 日志级别
        feat.append(log.count('[INFO]'))
        feat.append(log.count('[ERROR]'))
        feat.append(log.count('[WARN]'))
        feat.append(log.count('[DEBUG]'))
        
        # 5G模块
        feat.append(log.count('[AMF]'))
        feat.append(log.count('[SMF]'))
        feat.append(log.count('[UPF]'))
        feat.append(log.count('[PCF]'))
        feat.append(log.count('[UDM]'))
        feat.append(log.count('[UDR]'))
        
        # HTTP状态码
        feat.append(log.count('| 200 |'))
        feat.append(log.count('| 201 |'))
        feat.append(log.count('| 204 |'))
        feat.append(log.count('| 400 |'))
        feat.append(log.count('| 404 |'))
        feat.append(log.count('| 500 |'))
        
        # 关键词
        feat.append(log.lower().count('failed'))
        
        features.append(feat)
    
    return np.array(features)

def extract_text_features(logs, max_features=500):
    """提取文本特征"""
    print("提取文本特征...")
    
    # 预处理文本
    processed_logs = []
    for log in logs:
        # 截取前5000字符
        text = log[:5000] if log else ""
        # 简单清理
        text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIME>', text)
        text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
        processed_logs.append(text)
    
    # TF-IDF
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        ngram_range=(1, 2),
        min_df=2,
        max_df=0.9
    )
    
    tfidf_features = vectorizer.fit_transform(processed_logs)
    return tfidf_features.toarray(), vectorizer

def train_model(X, y):
    """训练模型"""
    print("训练模型...")
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 训练XGBoost
    model = xgb.XGBClassifier(
        n_estimators=100,
        max_depth=6,
        learning_rate=0.1,
        random_state=42,
        eval_metric='mlogloss'
    )
    
    model.fit(X_train_scaled, y_train)
    
    # 评估
    y_pred = model.predict(X_test_scaled)
    f1_macro = f1_score(y_test, y_pred, average='macro')
    
    print(f"F1-macro: {f1_macro:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    return model, scaler, f1_macro

def predict_test_set(model, scaler, vectorizer):
    """预测测试集"""
    print("预测测试集...")
    
    test_dir = 'files/test'
    if not os.path.exists(test_dir):
        print("测试目录不存在")
        return None
    
    test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
    test_files.sort(key=lambda x: int(x.split('.')[0]))
    
    test_logs = []
    test_ids = []
    
    for file_name in test_files:
        log_id = int(file_name.split('.')[0])
        file_path = os.path.join(test_dir, file_name)
        
        # 读取文件
        content = ""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()
                break
            except UnicodeDecodeError:
                continue
        
        # 解析JSON
        if content.startswith('{'):
            try:
                json_data = json.loads(content)
                if 'content' in json_data:
                    content = json_data['content']
            except:
                pass
        
        test_logs.append(content)
        test_ids.append(log_id)
    
    # 提取特征
    simple_features = extract_simple_features(test_logs)
    text_features, _ = extract_text_features(test_logs)
    
    # 合并特征
    X_test = np.hstack([simple_features, text_features])
    X_test_scaled = scaler.transform(X_test)
    
    # 预测
    predictions = model.predict(X_test_scaled)
    
    # 保存结果
    results_df = pd.DataFrame({
        '日志片段文件编号': test_ids,
        '故障类型': predictions.astype(int)
    })
    
    results_df = results_df.sort_values('日志片段文件编号')
    results_df.to_csv('result.csv', index=False, encoding='utf-8-sig')
    
    print(f"预测完成，保存到 result.csv")
    print("预测分布:")
    pred_counts = Counter(predictions)
    for fault_type, count in sorted(pred_counts.items()):
        print(f"  故障类型 {fault_type}: {count} 个")
    
    return results_df

def main():
    """主函数"""
    print("=== 简化5G日志异常检测系统 ===")
    
    # 1. 加载数据
    logs, labels = load_data()
    
    # 2. 数据分布
    print("\n数据分布:")
    label_counts = Counter(labels)
    for label, count in sorted(label_counts.items()):
        print(f"  故障类型 {label}: {count} 个样本")
    
    # 3. 特征提取
    simple_features = extract_simple_features(logs)
    text_features, vectorizer = extract_text_features(logs)
    
    # 4. 合并特征
    X = np.hstack([simple_features, text_features])
    y = np.array(labels)
    
    print(f"特征矩阵形状: {X.shape}")
    
    # 5. 训练模型
    model, scaler, f1_score = train_model(X, y)
    
    # 6. 预测测试集
    results = predict_test_set(model, scaler, vectorizer)
    
    print(f"\n训练完成！F1-macro: {f1_score:.4f}")
    
    return model, scaler, vectorizer, f1_score

if __name__ == "__main__":
    main()
