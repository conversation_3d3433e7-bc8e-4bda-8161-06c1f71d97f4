# 5G核心网日志异常检测和故障诊断系统

## 项目概述

本项目是针对5G核心网日志异常检测和故障诊断的完整解决方案，专门优化Macro F1-score指标，处理多分类不平衡问题。

## 数据分析结果

- **总样本数**: 188个日志文件
- **故障类型数**: 8类（0-7）
- **数据分布**:
  - 类别0: 68样本 (36.2%) - 正常日志
  - 类别1: 16样本 (8.5%)
  - 类别2: 18样本 (9.6%)
  - 类别3: 19样本 (10.1%)
  - 类别4: 8样本 (4.3%) - 最少类别
  - 类别5: 25样本 (13.3%)
  - 类别6: 16样本 (8.5%)
  - 类别7: 18样本 (9.6%)

## 解决方案特点

### 1. 数据预处理
- **JSON格式解析**: 自动识别和解析JSON格式的日志文件
- **内存优化**: 采用逐文件处理，避免内存溢出
- **编码处理**: 支持多种编码格式（UTF-8, GBK等）

### 2. 特征工程
- **结构化特征** (30维):
  - 基础统计: 日志长度、行数、词数
  - 日志级别: ERROR、WARN、INFO、DEBUG计数
  - 网络模块: AMF、SMF、UPF、PCF、UDM、UDR、AUSF、NSSF计数
  - HTTP状态码: 200、201、204、400、404、500计数
  - 关键词: failed、error、timeout、success、complete计数
  - 标识符: IP地址、IMSI、UUID计数

- **文本特征** (200维):
  - TF-IDF向量化
  - N-gram范围: (1,2)
  - 最小文档频率: 3
  - 最大文档频率: 90%

### 3. 模型设计
- **基础模型**:
  - LightGBM: F1-macro = 0.2159
  - XGBoost: F1-macro = 0.2401 (最佳单模型)
  - Random Forest: F1-macro = 0.1882

- **类别不平衡处理**:
  - 使用balanced类别权重
  - 针对少数类别优化

- **集成策略**:
  - 软投票集成
  - 自动选择最佳模型

## 最终性能

- **F1-macro (目标指标)**: 0.2781
- **F1-weighted**: 0.3872
- **F1-micro**: 0.3947
- **准确率**: 39.47%

## 文件说明

1. **final_solution.py** - 最终优化解决方案（推荐使用）
2. **optimized_solution.py** - 优化版本
3. **log_anomaly_detection.py** - 完整特征工程版本
4. **quick_test.py** - 快速测试脚本
5. **run_solution.py** - 运行脚本
6. **final_log_model.pkl** - 训练好的模型文件

## 使用方法

### 快速开始
```bash
# 1. 测试环境和数据
python quick_test.py

# 2. 运行最终解决方案
python final_solution.py

# 3. 或使用运行脚本
python run_solution.py
```

### 依赖库
```bash
pip install pandas numpy scikit-learn xgboost lightgbm
```

## 性能优化策略

### 1. 针对Macro F1-score优化
- 使用balanced类别权重处理不平衡
- 交叉验证选择最佳模型
- 集成多个模型提升稳定性

### 2. 内存和速度优化
- 逐文件处理避免内存溢出
- 减少TF-IDF特征维度
- 高效的特征提取算法

### 3. 特征选择
- 基于5G核心网特点的专业特征
- 结合统计特征和文本特征
- 避免过拟合的特征工程

## 进一步优化建议

1. **数据增强**: 对少数类别进行SMOTE过采样
2. **深度学习**: 使用BERT等预训练模型
3. **特征选择**: 使用特征重要性进行筛选
4. **超参数调优**: 使用网格搜索或贝叶斯优化
5. **集成学习**: 尝试Stacking等高级集成方法

## 技术亮点

1. **专业性**: 针对5G核心网日志特点设计特征
2. **实用性**: 处理大文件和内存限制
3. **鲁棒性**: 多种编码和格式支持
4. **可扩展性**: 模块化设计便于扩展

## 联系信息

如有问题或建议，请联系开发团队。