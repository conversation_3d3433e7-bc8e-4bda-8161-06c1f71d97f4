#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的5G日志异常检测训练脚本 - 优化F1分数
"""

import os
import json
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split, StratifiedKFold, cross_val_score
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.metrics import classification_report, f1_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
import xgboost as xgb
from collections import Counter
import re
from imblearn.over_sampling import SMOTE

def load_data():
    """加载数据"""
    print("加载数据...")
    
    # 加载标签
    label_file = 'files/train/label.csv'
    encodings = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']
    labels_df = None
    
    for encoding in encodings:
        try:
            labels_df = pd.read_csv(label_file, encoding=encoding)
            print(f"成功使用 {encoding} 编码加载标签文件: {len(labels_df)} 条记录")
            break
        except UnicodeDecodeError:
            continue
    
    if labels_df is None:
        labels_df = pd.read_csv(label_file, encoding='utf-8', errors='ignore')
    
    # 加载日志文件
    logs = []
    labels = []
    
    for _, row in labels_df.iterrows():
        log_id = int(row['日志片段文件编号'])
        label = int(row['故障类型'])
        log_file = f'files/train/{log_id}.txt'
        
        if os.path.exists(log_file):
            # 读取日志文件
            content = ""
            encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
            for encoding in encodings:
                try:
                    with open(log_file, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                    break
                except UnicodeDecodeError:
                    continue
            
            # 解析JSON格式
            if content.startswith('{'):
                try:
                    json_data = json.loads(content)
                    if 'content' in json_data:
                        content = json_data['content']
                except:
                    pass
            
            logs.append(content)
            labels.append(label)
    
    print(f"成功加载 {len(logs)} 个样本")
    return logs, labels

def extract_enhanced_features(logs):
    """提取增强特征"""
    print("提取增强特征...")
    
    features = []
    for log in logs:
        if not log:
            features.append([0] * 30)
            continue
            
        feat = []
        lines = log.split('\n')
        
        # 基础统计特征
        feat.append(len(log))  # 日志长度
        feat.append(len(lines))  # 行数
        feat.append(len(log.split()))  # 词数
        feat.append(np.mean([len(line) for line in lines]) if lines else 0)  # 平均行长
        
        # 日志级别计数和比例
        total_lines = len(lines)
        feat.append(log.count('[INFO]'))
        feat.append(log.count('[ERROR]'))
        feat.append(log.count('[WARN]'))
        feat.append(log.count('[DEBUG]'))
        feat.append(log.count('[TRACE]'))
        
        # 日志级别比例
        feat.append(log.count('[ERROR]') / total_lines if total_lines > 0 else 0)
        feat.append(log.count('[WARN]') / total_lines if total_lines > 0 else 0)
        
        # 5G核心网模块
        feat.append(log.count('[AMF]'))
        feat.append(log.count('[SMF]'))
        feat.append(log.count('[UPF]'))
        feat.append(log.count('[PCF]'))
        feat.append(log.count('[UDM]'))
        feat.append(log.count('[UDR]'))
        feat.append(log.count('[AUSF]'))
        feat.append(log.count('[NSSF]'))
        
        # HTTP状态码
        feat.append(log.count('| 200 |'))
        feat.append(log.count('| 201 |'))
        feat.append(log.count('| 204 |'))
        feat.append(log.count('| 400 |'))
        feat.append(log.count('| 404 |'))
        feat.append(log.count('| 500 |'))
        
        # 错误关键词
        text_lower = log.lower()
        feat.append(text_lower.count('failed'))
        feat.append(text_lower.count('error'))
        feat.append(text_lower.count('timeout'))
        feat.append(text_lower.count('exception'))
        
        # 网络相关
        feat.append(len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', log)))  # IP数量
        feat.append(len(re.findall(r'imsi-\d+', log)))  # IMSI数量
        
        features.append(feat)
    
    return np.array(features)

def extract_text_features(logs, max_features=1000):
    """提取文本特征"""
    print("提取文本特征...")
    
    # 预处理文本
    processed_logs = []
    for log in logs:
        # 截取前8000字符
        text = log[:8000] if log else ""
        # 保留更多信息的清理
        text = re.sub(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}[^\s]*', '<TIMESTAMP>', text)
        text = re.sub(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', '<IP>', text)
        text = re.sub(r'imsi-\d+', '<IMSI>', text)
        text = re.sub(r':\d{2,5}\b', ':<PORT>', text)
        processed_logs.append(text)
    
    # 更好的TF-IDF配置
    vectorizer = TfidfVectorizer(
        max_features=max_features,
        ngram_range=(1, 3),  # 包含3-gram
        min_df=2,
        max_df=0.8,
        sublinear_tf=True
    )
    
    tfidf_features = vectorizer.fit_transform(processed_logs)
    return tfidf_features.toarray(), vectorizer

def train_ensemble_model(X, y):
    """训练集成模型"""
    print("训练集成模型...")
    
    # 数据分割
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.2, random_state=42, stratify=y
    )
    
    # 标准化
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 处理类别不平衡
    try:
        unique_classes, counts = np.unique(y_train, return_counts=True)
        min_samples = min(counts)
        
        if min_samples >= 3:
            smote = SMOTE(random_state=42, k_neighbors=min(3, min_samples - 1))
            X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)
            print("应用SMOTE重采样")
        else:
            X_train_balanced, y_train_balanced = X_train_scaled, y_train
            print("跳过重采样（样本过少）")
    except Exception as e:
        print(f"重采样失败: {e}")
        X_train_balanced, y_train_balanced = X_train_scaled, y_train
    
    # 创建多个模型
    models = {
        'xgb': xgb.XGBClassifier(
            n_estimators=200,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            random_state=42,
            eval_metric='mlogloss'
        ),
        'rf': RandomForestClassifier(
            n_estimators=200,
            max_depth=12,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            class_weight='balanced'
        )
    }
    
    # 训练和评估每个模型
    trained_models = {}
    cv_scores = {}
    
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    for name, model in models.items():
        print(f"训练 {name}...")
        
        # 交叉验证
        scores = cross_val_score(model, X_train_balanced, y_train_balanced, 
                               cv=cv, scoring='f1_macro')
        cv_scores[name] = scores.mean()
        print(f"{name} CV F1-macro: {scores.mean():.4f} ± {scores.std():.4f}")
        
        # 训练完整模型
        model.fit(X_train_balanced, y_train_balanced)
        trained_models[name] = model
    
    # 选择最佳模型或创建集成
    best_model_name = max(cv_scores, key=cv_scores.get)
    best_model = trained_models[best_model_name]
    
    # 如果多个模型性能接近，创建集成
    if len([s for s in cv_scores.values() if s > max(cv_scores.values()) - 0.02]) > 1:
        print("创建集成模型...")
        ensemble = VotingClassifier(
            estimators=[(name, model) for name, model in trained_models.items()],
            voting='soft'
        )
        ensemble.fit(X_train_balanced, y_train_balanced)
        final_model = ensemble
    else:
        print(f"使用最佳单模型: {best_model_name}")
        final_model = best_model
    
    # 评估
    y_pred = final_model.predict(X_test_scaled)
    f1_macro = f1_score(y_test, y_pred, average='macro')
    
    print(f"最终F1-macro: {f1_macro:.4f}")
    print("\n分类报告:")
    print(classification_report(y_test, y_pred))
    
    return final_model, scaler, f1_macro

def predict_test_set(model, scaler, vectorizer):
    """预测测试集"""
    print("预测测试集...")
    
    test_dir = 'files/test'
    if not os.path.exists(test_dir):
        print("测试目录不存在")
        return None
    
    test_files = [f for f in os.listdir(test_dir) if f.endswith('.txt')]
    test_files.sort(key=lambda x: int(x.split('.')[0]))
    
    test_logs = []
    test_ids = []
    
    for file_name in test_files:
        log_id = int(file_name.split('.')[0])
        file_path = os.path.join(test_dir, file_name)
        
        # 读取文件
        content = ""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    content = f.read().strip()
                break
            except UnicodeDecodeError:
                continue
        
        # 解析JSON
        if content.startswith('{'):
            try:
                json_data = json.loads(content)
                if 'content' in json_data:
                    content = json_data['content']
            except:
                pass
        
        test_logs.append(content)
        test_ids.append(log_id)
    
    # 提取特征
    enhanced_features = extract_enhanced_features(test_logs)
    text_features, _ = extract_text_features(test_logs)
    
    # 合并特征
    X_test = np.hstack([enhanced_features, text_features])
    X_test_scaled = scaler.transform(X_test)
    
    # 预测
    predictions = model.predict(X_test_scaled)
    
    # 保存结果
    results_df = pd.DataFrame({
        '日志片段文件编号': test_ids,
        '故障类型': predictions.astype(int)
    })
    
    results_df = results_df.sort_values('日志片段文件编号')
    results_df.to_csv('result_improved.csv', index=False, encoding='utf-8-sig')
    
    print(f"预测完成，保存到 result_improved.csv")
    print("预测分布:")
    pred_counts = Counter(predictions)
    for fault_type, count in sorted(pred_counts.items()):
        print(f"  故障类型 {fault_type}: {count} 个")
    
    return results_df

def main():
    """主函数"""
    print("=== 改进的5G日志异常检测系统 ===")
    
    # 1. 加载数据
    logs, labels = load_data()
    
    # 2. 数据分布
    print("\n数据分布:")
    label_counts = Counter(labels)
    for label, count in sorted(label_counts.items()):
        print(f"  故障类型 {label}: {count} 个样本 ({count/len(labels)*100:.1f}%)")
    
    # 3. 特征提取
    enhanced_features = extract_enhanced_features(logs)
    text_features, vectorizer = extract_text_features(logs)
    
    # 4. 合并特征
    X = np.hstack([enhanced_features, text_features])
    y = np.array(labels)
    
    print(f"特征矩阵形状: {X.shape}")
    
    # 5. 训练模型
    model, scaler, f1_score = train_ensemble_model(X, y)
    
    # 6. 预测测试集
    results = predict_test_set(model, scaler, vectorizer)
    
    print(f"\n训练完成！最终F1-macro: {f1_score:.4f}")
    
    return model, scaler, vectorizer, f1_score

if __name__ == "__main__":
    main()
