#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试版本 - 验证数据加载和基础功能
"""

import os
import json
import pandas as pd
from collections import Counter

def quick_test():
    """快速测试数据加载和分析"""
    print("=== 快速测试 ===")

    # 检查数据目录
    data_dir = 'files/train'
    label_file = os.path.join(data_dir, 'label.csv')

    if not os.path.exists(data_dir):
        print(f"错误: 数据目录 {data_dir} 不存在")
        return False

    if not os.path.exists(label_file):
        print(f"错误: 标签文件 {label_file} 不存在")
        return False

    # 加载标签
    try:
        labels_df = pd.read_csv(label_file, encoding='utf-8-sig')
        labels_df.columns = ['file_id', 'fault_type']
        print(f"✓ 标签文件加载成功: {len(labels_df)} 条记录")
    except:
        try:
            labels_df = pd.read_csv(label_file, encoding='gbk')
            labels_df.columns = ['file_id', 'fault_type']
            print(f"✓ 标签文件加载成功 (GBK编码): {len(labels_df)} 条记录")
        except Exception as e:
            print(f"✗ 标签文件加载失败: {e}")
            return False

    # 分析标签分布
    fault_counter = Counter(labels_df['fault_type'])
    print("\n标签分布:")
    total = len(labels_df)
    for fault_type, count in sorted(fault_counter.items()):
        percentage = (count / total) * 100
        print(f"  故障类型 {fault_type}: {count} 个样本 ({percentage:.1f}%)")

    # 检查日志文件
    log_files = [f for f in os.listdir(data_dir) if f.endswith('.txt')]
    print(f"\n✓ 找到 {len(log_files)} 个日志文件")

    # 测试加载几个日志文件
    loaded_count = 0
    sample_contents = []

    for i, log_file in enumerate(log_files[:5]):  # 只测试前5个文件
        file_id = int(log_file.split('.')[0])
        file_path = os.path.join(data_dir, log_file)

        try:
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read().strip()

            # 解析JSON格式
            if content.startswith('{'):
                try:
                    json_data = json.loads(content)
                    log_content = json_data.get('content', '')
                    print(f"  文件 {log_file}: JSON格式, 内容长度 {len(log_content)}")
                except:
                    log_content = content
                    print(f"  文件 {log_file}: 文本格式, 内容长度 {len(log_content)}")
            else:
                log_content = content
                print(f"  文件 {log_file}: 纯文本格式, 内容长度 {len(log_content)}")

            sample_contents.append(log_content[:200])  # 保存前200个字符作为样本
            loaded_count += 1

        except Exception as e:
            print(f"  文件 {log_file}: 加载失败 - {e}")

    print(f"\n✓ 成功测试加载 {loaded_count} 个日志文件")

    # 显示样本内容
    if sample_contents:
        print(f"\n样本日志内容 (前200字符):")
        print("-" * 50)
        print(sample_contents[0])
        print("-" * 50)

    # 检查依赖库
    print("\n检查依赖库:")
    required_libs = ['pandas', 'numpy', 'sklearn', 'xgboost', 'lightgbm']

    for lib in required_libs:
        try:
            __import__(lib)
            print(f"  ✓ {lib}")
        except ImportError:
            print(f"  ✗ {lib} (需要安装)")

    print("\n=== 测试完成 ===")
    return True

if __name__ == "__main__":
    quick_test()