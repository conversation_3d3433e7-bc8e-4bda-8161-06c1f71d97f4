#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终优化的5G核心网日志异常检测解决方案
针对大文件和内存优化，专注于Macro F1-score
"""

import os
import json
import re
import pandas as pd
import numpy as np
from datetime import datetime
from collections import Counter
import warnings
import gc
warnings.filterwarnings('ignore')

from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.ensemble import RandomForestClassifier, VotingClassifier
from sklearn.metrics import f1_score, classification_report
from sklearn.utils.class_weight import compute_class_weight
import xgboost as xgb
import lightgbm as lgb

class FinalLogDetector:
    """最终优化的日志异常检测器"""

    def __init__(self, data_dir='files/train'):
        self.data_dir = data_dir
        self.label_file = os.path.join(data_dir, 'label.csv')
        self.models = {}
        self.best_model = None
        self.scaler = None
        self.vectorizer = None
        self.feature_names = None

    def load_data_efficiently(self):
        """高效加载数据，避免内存问题"""
        print("步骤1: 高效加载数据...")

        # 加载标签
        try:
            labels_df = pd.read_csv(self.label_file, encoding='gbk')
            labels_df.columns = ['file_id', 'fault_type']
        except:
            labels_df = pd.read_csv(self.label_file, encoding='utf-8-sig')
            labels_df.columns = ['file_id', 'fault_type']

        print(f"标签数据: {len(labels_df)} 条")

        # 分析类别分布
        fault_counter = Counter(labels_df['fault_type'])
        print("类别分布:")
        total = len(labels_df)
        for fault_type, count in sorted(fault_counter.items()):
            percentage = (count / total) * 100
            print(f"  类别 {fault_type}: {count} 样本 ({percentage:.1f}%)")

        # 逐个加载日志文件并提取特征
        features_list = []
        texts = []
        labels = []

        log_files = [f for f in os.listdir(self.data_dir) if f.endswith('.txt')]
        log_files.sort(key=lambda x: int(x.split('.')[0]))

        print(f"开始处理 {len(log_files)} 个日志文件...")

        for i, log_file in enumerate(log_files):
            if i % 20 == 0:
                print(f"  处理进度: {i+1}/{len(log_files)}")

            file_id = int(log_file.split('.')[0])
            file_path = os.path.join(self.data_dir, log_file)

            # 获取标签
            label_row = labels_df[labels_df['file_id'] == file_id]
            if label_row.empty:
                continue

            fault_type = label_row.iloc[0]['fault_type']

            try:
                # 读取文件
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read().strip()

                # 解析JSON
                if content.startswith('{'):
                    try:
                        json_data = json.loads(content)
                        log_content = json_data.get('content', '')
                    except:
                        log_content = content
                else:
                    log_content = content

                # 提取特征（内存友好）
                features = self._extract_efficient_features(log_content)
                features_list.append(features)

                # 保存文本（截断以节省内存）
                text_sample = log_content[:10000]  # 只保留前10000字符
                texts.append(text_sample)
                labels.append(fault_type)

                # 清理内存
                del log_content, content
                if i % 50 == 0:
                    gc.collect()

            except Exception as e:
                print(f"  跳过文件 {log_file}: {e}")
                continue

        print(f"成功处理 {len(features_list)} 个样本")

        # 转换为DataFrame
        features_df = pd.DataFrame(features_list)

        return features_df, texts, labels

    def _extract_efficient_features(self, content):
        """提取高效的特征，避免复杂计算"""
        features = {}

        # 基础统计特征
        features['log_length'] = len(content)
        features['line_count'] = content.count('\n') + 1
        features['word_count'] = len(content.split())

        # 日志级别计数
        features['error_count'] = content.count('[ERROR]')
        features['warn_count'] = content.count('[WARN]')
        features['info_count'] = content.count('[INFO]')
        features['debug_count'] = content.count('[DEBU]')

        # 网络模块计数
        modules = ['AMF', 'SMF', 'UPF', 'PCF', 'UDM', 'UDR', 'AUSF', 'NSSF']
        for module in modules:
            features[f'{module.lower()}_count'] = content.count(f'[{module}]')

        # HTTP状态码
        features['http_200_count'] = content.count('| 200 |')
        features['http_201_count'] = content.count('| 201 |')
        features['http_204_count'] = content.count('| 204 |')
        features['http_400_count'] = content.count('| 400 |')
        features['http_404_count'] = content.count('| 404 |')
        features['http_500_count'] = content.count('| 500 |')

        # 关键词计数
        features['failed_count'] = content.lower().count('failed')
        features['error_word_count'] = content.lower().count('error')
        features['timeout_count'] = content.lower().count('timeout')
        features['success_count'] = content.lower().count('success')
        features['complete_count'] = content.lower().count('complete')

        # 时间相关
        time_matches = re.findall(r'\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}', content)
        features['time_entries'] = len(time_matches)

        # IP地址和标识符
        features['ip_count'] = len(re.findall(r'\b(?:\d{1,3}\.){3}\d{1,3}\b', content))
        features['imsi_count'] = len(re.findall(r'imsi-\d+', content))
        features['uuid_count'] = len(re.findall(r'urn:uuid:', content))

        return features

    def create_text_features(self, texts):
        """创建文本特征"""
        print("步骤2: 创建文本特征...")

        # 使用较小的TF-IDF特征集
        self.vectorizer = TfidfVectorizer(
            max_features=200,  # 减少特征数量
            ngram_range=(1, 2),
            min_df=3,
            max_df=0.9,
            stop_words=None
        )

        tfidf_features = self.vectorizer.fit_transform(texts)
        print(f"TF-IDF特征: {tfidf_features.shape[1]} 维")

        return tfidf_features

    def train_models(self, features_df, tfidf_features, labels):
        """训练多个模型"""
        print("步骤3: 训练模型...")

        # 准备数据
        X_struct = features_df.fillna(0)
        X_text = tfidf_features.toarray()

        # 合并特征
        X = np.hstack([X_struct.values, X_text])
        y = np.array(labels)

        # 标准化
        self.scaler = StandardScaler()
        X_scaled = self.scaler.fit_transform(X)

        # 保存特征名称
        struct_names = X_struct.columns.tolist()
        text_names = [f'tfidf_{i}' for i in range(X_text.shape[1])]
        self.feature_names = struct_names + text_names

        print(f"总特征数: {X_scaled.shape[1]}")
        print(f"样本数: {X_scaled.shape[0]}")

        # 计算类别权重
        classes = np.unique(y)
        class_weights = compute_class_weight('balanced', classes=classes, y=y)
        class_weight_dict = dict(zip(classes, class_weights))

        # 定义模型
        models = {
            'lightgbm': lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1,
                verbose=-1
            ),
            'xgboost': xgb.XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.1,
                subsample=0.8,
                colsample_bytree=0.8,
                random_state=42,
                n_jobs=-1
            ),
            'random_forest': RandomForestClassifier(
                n_estimators=150,
                max_depth=10,
                min_samples_split=5,
                min_samples_leaf=2,
                class_weight='balanced',
                random_state=42,
                n_jobs=-1
            )
        }

        # 交叉验证评估
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        best_score = 0
        best_model_name = None

        for name, model in models.items():
            print(f"\n训练 {name}...")

            scores = cross_val_score(model, X_scaled, y, cv=cv,
                                   scoring='f1_macro', n_jobs=-1)

            avg_score = scores.mean()
            std_score = scores.std()

            print(f"{name}: F1-macro = {avg_score:.4f} (+/- {std_score*2:.4f})")

            if avg_score > best_score:
                best_score = avg_score
                best_model_name = name

            # 训练完整模型
            model.fit(X_scaled, y)
            self.models[name] = model

        print(f"\n最佳单模型: {best_model_name} (F1-macro: {best_score:.4f})")
        self.best_model = self.models[best_model_name]

        return X_scaled, y, best_score

    def create_ensemble(self, X, y):
        """创建集成模型"""
        print("步骤4: 创建集成模型...")

        # 选择表现最好的几个模型
        estimators = [
            ('lgb', self.models['lightgbm']),
            ('xgb', self.models['xgboost']),
            ('rf', self.models['random_forest'])
        ]

        # 创建投票分类器
        ensemble = VotingClassifier(
            estimators=estimators,
            voting='soft'
        )

        # 评估集成模型
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        ensemble_scores = cross_val_score(ensemble, X, y, cv=cv,
                                        scoring='f1_macro', n_jobs=-1)

        ensemble_score = ensemble_scores.mean()
        print(f"集成模型 F1-macro: {ensemble_score:.4f} (+/- {ensemble_scores.std()*2:.4f})")

        # 训练集成模型
        ensemble.fit(X, y)
        self.models['ensemble'] = ensemble

        # 如果集成模型更好，则使用集成模型
        if ensemble_score > max([cross_val_score(model, X, y, cv=cv, scoring='f1_macro').mean()
                                for model in [self.models['lightgbm'], self.models['xgboost'],
                                            self.models['random_forest']]]):
            self.best_model = ensemble
            print("使用集成模型作为最终模型")
            return ensemble_score
        else:
            print("保持单模型作为最终模型")
            return max([cross_val_score(model, X, y, cv=cv, scoring='f1_macro').mean()
                       for model in [self.models['lightgbm'], self.models['xgboost'],
                                   self.models['random_forest']]])

    def detailed_evaluation(self, X, y):
        """详细评估最佳模型"""
        print("步骤5: 详细评估...")

        from sklearn.model_selection import train_test_split

        # 划分训练集和测试集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )

        # 重新训练最佳模型
        self.best_model.fit(X_train, y_train)

        # 预测
        y_pred = self.best_model.predict(X_test)

        # 计算各种F1分数
        f1_macro = f1_score(y_test, y_pred, average='macro')
        f1_weighted = f1_score(y_test, y_pred, average='weighted')
        f1_micro = f1_score(y_test, y_pred, average='micro')

        print(f"\n=== 最终评估结果 ===")
        print(f"F1-macro (目标指标): {f1_macro:.4f}")
        print(f"F1-weighted: {f1_weighted:.4f}")
        print(f"F1-micro: {f1_micro:.4f}")

        # 详细分类报告
        print(f"\n详细分类报告:")
        print(classification_report(y_test, y_pred))

        return f1_macro

    def run_complete_training(self):
        """运行完整训练流程"""
        print("=== 最终优化的5G日志异常检测系统 ===")
        print("开始训练...\n")

        try:
            # 1. 高效加载数据
            features_df, texts, labels = self.load_data_efficiently()

            # 2. 创建文本特征
            tfidf_features = self.create_text_features(texts)

            # 3. 训练模型
            X, y, best_single_score = self.train_models(features_df, tfidf_features, labels)

            # 4. 创建集成模型
            final_score = self.create_ensemble(X, y)

            # 5. 详细评估
            evaluation_score = self.detailed_evaluation(X, y)

            print(f"\n=== 训练完成 ===")
            print(f"最终F1-macro分数: {evaluation_score:.4f}")

            return {
                'final_score': evaluation_score,
                'cv_score': final_score,
                'model': self.best_model,
                'scaler': self.scaler,
                'vectorizer': self.vectorizer,
                'feature_names': self.feature_names
            }

        except Exception as e:
            print(f"训练过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
            return None

    def save_model(self, filename='final_log_model.pkl'):
        """保存模型"""
        import pickle

        model_data = {
            'model': self.best_model,
            'scaler': self.scaler,
            'vectorizer': self.vectorizer,
            'feature_names': self.feature_names
        }

        with open(filename, 'wb') as f:
            pickle.dump(model_data, f)

        print(f"模型已保存到 {filename}")

def main():
    """主函数"""
    detector = FinalLogDetector()

    # 运行训练
    results = detector.run_complete_training()

    if results is None:
        print("训练失败！")
        return

    # 保存模型
    detector.save_model()

    print(f"\n=== 总结 ===")
    print(f"最终F1-macro分数: {results['final_score']:.4f}")
    print(f"交叉验证分数: {results['cv_score']:.4f}")
    print("模型训练完成并已保存！")

    # 内存清理
    import gc
    gc.collect()

if __name__ == "__main__":
    main()